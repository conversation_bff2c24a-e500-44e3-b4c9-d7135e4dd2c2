/* Properties Enhanced Styling */

/* ===== PROPERTY INDEX PAGE ===== */

/* Hero Section Enhancements */
.properties-hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.properties-hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-10px) rotate(1deg); }
}

/* Search Form Enhancements */
.search-form {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.search-input {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.search-input:focus {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(59, 130, 246, 0.3);
}

.search-input::placeholder {
  transition: opacity 0.3s ease;
}

.search-input:focus::placeholder {
  opacity: 0.7;
}

/* Property Cards Enhanced */
.property-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.property-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
  z-index: 1;
}

.property-card:hover::before {
  left: 100%;
}

.property-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(59, 130, 246, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(59, 130, 246, 0.2);
}

/* Property Image Enhancements */
.property-image {
  position: relative;
  overflow: hidden;
  background: linear-gradient(45deg, #f1f5f9, #e2e8f0);
}

.property-image img {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.property-card:hover .property-image img {
  transform: scale(1.1) rotate(1deg);
  filter: brightness(1.1) contrast(1.05);
}

/* Property Badges */
.property-badge {
  backdrop-filter: blur(10px);
  background: rgba(59, 130, 246, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
}

.property-badge:hover {
  transform: scale(1.05);
  background: rgba(59, 130, 246, 1);
}

.price-badge {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.property-card:hover .price-badge {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Favorite Button Enhancement */
.favorite-btn {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.favorite-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, rgba(239, 68, 68, 0.2) 0%, transparent 70%);
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.favorite-btn:hover::before {
  width: 100px;
  height: 100px;
}

.favorite-btn:hover {
  transform: scale(1.1);
  background: rgba(255, 255, 255, 1);
  color: #ef4444;
}

/* Property Content Enhancements */
.property-content {
  position: relative;
  z-index: 2;
}

.property-title {
  transition: color 0.3s ease;
  position: relative;
}

.property-title::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  transition: width 0.3s ease;
}

.property-card:hover .property-title::after {
  width: 100%;
}

/* Feature Icons Animation */
.feature-icon {
  transition: all 0.3s ease;
}

.property-card:hover .feature-icon {
  transform: scale(1.1);
  color: #3b82f6;
}

/* Action Buttons */
.action-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.action-btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.action-btn-primary:hover::before {
  left: 100%;
}

.action-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.4);
}

.action-btn-secondary {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn-secondary:hover {
  transform: scale(1.05);
  background: #f3f4f6;
}

/* ===== PROPERTY SHOW PAGE ===== */

/* Header Navigation Enhancement */
.property-nav {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

.back-btn {
  transition: all 0.3s ease;
  position: relative;
}

.back-btn:hover {
  transform: translateX(-4px);
}

.back-btn svg {
  transition: transform 0.3s ease;
}

.back-btn:hover svg {
  transform: translateX(-2px);
}

/* Property Header Enhancements */
.property-header {
  position: relative;
}

.property-title-main {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.property-price {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

/* Stats Cards Enhancement */
.stats-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover::before {
  opacity: 1;
}

.stats-card:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.stats-icon {
  transition: all 0.3s ease;
}

.stats-card:hover .stats-icon {
  transform: scale(1.1) rotate(5deg);
}

/* Image Gallery Enhancement */
.image-gallery {
  position: relative;
  overflow: hidden;
  border-radius: 1rem;
}

.gallery-image {
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.gallery-image:hover {
  transform: scale(1.05);
  filter: brightness(1.1) contrast(1.05);
}

/* Amenities Enhancement */
.amenity-item {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.amenity-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(34, 197, 94, 0.1), transparent);
  transition: left 0.6s ease;
}

.amenity-item:hover::before {
  left: 100%;
}

.amenity-item:hover {
  transform: translateX(4px);
  background: rgba(34, 197, 94, 0.05);
  border-color: rgba(34, 197, 94, 0.2);
}

.amenity-icon {
  transition: all 0.3s ease;
}

.amenity-item:hover .amenity-icon {
  transform: scale(1.2) rotate(10deg);
  background: rgba(34, 197, 94, 0.2);
}

/* Property Features Enhancement */
.feature-row {
  transition: all 0.3s ease;
  position: relative;
}

.feature-row:hover {
  transform: translateX(4px);
  background: rgba(59, 130, 246, 0.05);
}

/* Booking Card Enhancement */
.booking-card {
  background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.booking-card:hover {
  transform: translateY(-4px);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(59, 130, 246, 0.1);
}

/* Action Buttons Enhancement */
.contact-btn {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.contact-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.contact-btn:hover::before {
  left: 100%;
}

.contact-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
  .property-card:hover {
    transform: translateY(-4px) scale(1.01);
  }
  
  .stats-card:hover {
    transform: translateY(-2px) scale(1.01);
  }
}

/* Loading States */
.property-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Accessibility Enhancements */
.property-card:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

.action-btn-primary:focus,
.contact-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .property-card {
    border: 2px solid #000;
  }
  
  .property-badge,
  .price-badge {
    border: 2px solid #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .property-card,
  .stats-card,
  .amenity-item,
  .action-btn-primary,
  .contact-btn {
    transition: none;
  }
  
  .property-card:hover,
  .stats-card:hover {
    transform: none;
  }
}

/* Print Styles */
@media print {
  .property-card {
    break-inside: avoid;
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .action-btn-primary,
  .contact-btn,
  .favorite-btn {
    display: none;
  }
}