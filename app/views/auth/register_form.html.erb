<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full">
    <!-- <PERSON><PERSON> and Header -->
    <div class="text-center mb-8">
      <div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
      </div>
      <h2 class="text-3xl font-bold text-gray-900 mb-2">Join <PERSON></h2>
      <p class="text-gray-600">Create your account to start managing properties</p>
    </div>

    <!-- Registration Form -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
      <%= form_with url: register_path, method: :post, local: true, data: { turbo: false }, class: "space-y-6" do |form| %>
        <div class="space-y-4">
          <div>
            <%= form.label :name, "Full Name", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.text_field :name, autocomplete: "name", required: true, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                placeholder: "Enter your full name" %>
          </div>
          
          <div>
            <%= form.label :email, "Email Address", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.email_field :email, autocomplete: "email", required: true, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                placeholder: "Enter your email" %>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.password_field :password, autocomplete: "new-password", required: true, 
                  class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                  placeholder: "Password" %>
            </div>
            
            <div>
              <%= form.label :password_confirmation, "Confirm Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
              <%= form.password_field :password_confirmation, autocomplete: "new-password", required: true, 
                  class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                  placeholder: "Confirm" %>
            </div>
          </div>
          
          <!-- User Type Selection -->
          <div>
            <%= form.label :user_type, "I am a:", class: "block text-sm font-medium text-gray-700 mb-3" %>
            <div class="grid grid-cols-2 gap-4">
              <label class="relative">
                <%= form.radio_button :user_type, "tenant", checked: true, class: "sr-only peer" %>
                <div class="p-4 border-2 border-gray-300 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-gray-400 transition duration-200">
                  <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-gray-400 peer-checked:text-blue-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Tenant</span>
                    <p class="text-xs text-gray-500 mt-1">Looking for properties</p>
                  </div>
                </div>
              </label>
              
              <label class="relative">
                <%= form.radio_button :user_type, "landlord", class: "sr-only peer" %>
                <div class="p-4 border-2 border-gray-300 rounded-lg cursor-pointer peer-checked:border-blue-500 peer-checked:bg-blue-50 hover:border-gray-400 transition duration-200">
                  <div class="text-center">
                    <svg class="mx-auto h-8 w-8 text-gray-400 peer-checked:text-blue-500 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H9m0 0H5m0 0h2M7 7h10M7 11h10M7 15h10"></path>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Landlord</span>
                    <p class="text-xs text-gray-500 mt-1">Managing properties</p>
                  </div>
                </div>
              </label>
            </div>
          </div>
        </div>

        <div class="flex items-center">
          <input type="checkbox" id="terms" name="terms" required class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
          <label for="terms" class="ml-2 block text-sm text-gray-700">
            I agree to the 
            <a href="#" class="text-blue-600 hover:text-blue-500">Terms of Service</a> and 
            <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
          </label>
        </div>

        <div>
          <%= form.submit "Create Account", class: "w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200" %>
        </div>

        <div class="text-center">
          <span class="text-gray-600">Already have an account?</span>
          <%= link_to "Sign in here", login_path, class: "font-medium text-blue-600 hover:text-blue-500 ml-1" %>
        </div>
      <% end %>
    </div>
  </div>
</div>