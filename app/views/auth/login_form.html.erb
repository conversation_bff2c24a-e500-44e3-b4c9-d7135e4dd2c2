<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full">
    <!-- <PERSON><PERSON> and Header -->
    <div class="text-center mb-8">
      <div class="mx-auto h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center mb-4">
        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
        </svg>
      </div>
      <h2 class="text-3xl font-bold text-gray-900 mb-2">Welcome to Ofie</h2>
      <p class="text-gray-600">Sign in to access your property dashboard</p>
    </div>

    <!-- Login Form -->
    <div class="bg-white rounded-2xl shadow-xl p-8">
      <%= form_with url: login_path, method: :post, local: true, data: { turbo: false }, class: "space-y-6" do |form| %>
        <div class="space-y-4">
          <div>
            <%= form.label :email, "Email Address", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.email_field :email, autocomplete: "email", required: true, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                placeholder: "Enter your email" %>
          </div>
          
          <div>
            <%= form.label :password, "Password", class: "block text-sm font-medium text-gray-700 mb-2" %>
            <%= form.password_field :password, autocomplete: "current-password", required: true, 
                class: "w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition duration-200", 
                placeholder: "Enter your password" %>
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input type="checkbox" id="remember_me" name="remember" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
            <label for="remember_me" class="ml-2 block text-sm text-gray-700">Remember me</label>
          </div>
          <div class="text-sm">
            <%= link_to forgot_password_path, class: "font-medium text-blue-600 hover:text-blue-500" do %>
              Forgot your password?
            <% end %>
          </div>
        </div>

        <div>
          <%= form.submit "Sign In", class: "w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200" %>
        </div>

        <div class="text-center">
          <span class="text-gray-600">Don't have an account?</span>
          <%= link_to "Sign up here", register_path, class: "font-medium text-blue-600 hover:text-blue-500 ml-1" %>
        </div>
      <% end %>
    </div>

    <!-- Additional Info -->
    <div class="text-center mt-8">
      <p class="text-sm text-gray-500">
        By signing in, you agree to our 
        <a href="#" class="text-blue-600 hover:text-blue-500">Terms of Service</a> and 
        <a href="#" class="text-blue-600 hover:text-blue-500">Privacy Policy</a>
      </p>
    </div>
  </div>
</div>