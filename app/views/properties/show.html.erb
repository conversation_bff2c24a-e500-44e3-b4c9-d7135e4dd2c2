<% content_for :title, @property.title %>
<% content_for :meta_description, truncate(@property.description, length: 160) %>

<!-- Streamlined Hero Section -->
<div class="bg-white">
  <div class="w-full">
    <!-- Compact Image Gallery -->
    <div class="relative bg-black overflow-hidden mb-6" data-controller="gallery">
      <!-- Main Image - Smaller Height, Full Width -->
      <div class="aspect-[16/6] relative">
        <% if @property.photos.attached? && @property.photos.any? %>
          <% @property.photos.each_with_index do |image, index| %>
            <div class="<%= index == 0 ? 'block' : 'hidden' %> w-full h-full" data-gallery-target="slide" data-slide-index="<%= index %>">
              <%= image_tag image, 
                           class: "w-full h-full object-cover", 
                           alt: "#{@property.title} - Image #{index + 1}" %>
            </div>
          <% end %>
        <% else %>
          <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
            <div class="text-center text-gray-500">
              <svg class="w-12 h-12 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              <p class="text-sm font-medium">No images available</p>
            </div>
          </div>
        <% end %>
        
        <!-- Floating Action Buttons -->
        <div class="absolute top-4 right-4 flex space-x-2 z-10">
          <% if user_signed_in? %>
            <button class="bg-white bg-opacity-90 backdrop-blur-sm p-2.5 rounded-full shadow-sm hover:bg-opacity-100 transition-all duration-200 group">
              <svg class="w-5 h-5 text-gray-600 group-hover:text-red-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </button>
          <% end %>
          
          <button class="bg-white bg-opacity-90 backdrop-blur-sm p-2.5 rounded-full shadow-sm hover:bg-opacity-100 transition-all duration-200 group">
            <svg class="w-5 h-5 text-gray-600 group-hover:text-blue-500 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
            </svg>
          </button>
        </div>
        
        <!-- Navigation Arrows -->
        <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
          <button class="absolute left-3 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                  data-action="click->gallery#previousSlide">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>
          
          <button class="absolute right-3 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-all"
                  data-action="click->gallery#nextSlide">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        <% end %>
        
        <!-- Image Counter -->
        <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
          <div class="absolute bottom-3 right-3 bg-black bg-opacity-50 text-white px-2.5 py-1 rounded-full text-xs">
            <span data-gallery-target="counter">1</span> / <%= @property.photos.count %>
          </div>
        <% end %>
      </div>
      
      <!-- Centered Larger Thumbnail Navigation -->
      <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
        <div class="py-6 bg-gray-50">
          <div class="max-w-4xl mx-auto px-4">
            <div class="flex justify-center space-x-4 overflow-x-auto pb-1">
              <% @property.photos.each_with_index do |image, index| %>
                <button class="flex-shrink-0 w-32 h-24 rounded-lg overflow-hidden border-2 transition-all duration-200 <%= index == 0 ? 'border-blue-500 opacity-100' : 'border-transparent opacity-70 hover:opacity-100' %>"
                        data-gallery-target="thumbnail"
                        data-action="click->gallery#goToSlide"
                        data-slide-index="<%= index %>">
                  <%= image_tag image, 
                               class: "w-full h-full object-cover", 
                               alt: "#{@property.title} - Thumbnail #{index + 1}" %>
                </button>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Main Content Layout -->
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-8">
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Main Content Column -->
    <div class="lg:col-span-2 space-y-6">
      
      <!-- Property Header -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between mb-4">
          <div class="flex-1">
            <h1 class="text-2xl font-bold text-gray-900 mb-2"><%= @property.title %></h1>
            <div class="flex items-center text-gray-600 mb-3">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              <span class="text-sm"><%= @property.address %>, <%= @property.city %></span>
            </div>
            
            <!-- Property Stats -->
            <div class="flex flex-wrap gap-4 text-sm">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                </svg>
                <span class="font-medium"><%= @property.bedrooms %></span>
                <span class="text-gray-600 ml-1">bed</span>
              </div>
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11"></path>
                </svg>
                <span class="font-medium"><%= @property.bathrooms %></span>
                <span class="text-gray-600 ml-1">bath</span>
              </div>
              <% if @property.square_feet.present? %>
                <div class="flex items-center">
                  <svg class="w-4 h-4 mr-1.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                  </svg>
                  <span class="font-medium"><%= number_with_delimiter(@property.square_feet) %></span>
                  <span class="text-gray-600 ml-1">sq ft</span>
                </div>
              <% end %>
            </div>
          </div>
          
          <!-- Price Badge -->
          <div class="mt-4 sm:mt-0 sm:ml-6">
            <div class="bg-gradient-to-r from-blue-600 to-blue-700 text-white px-5 py-3 rounded-xl text-center">
              <div class="text-2xl font-bold">$<%= number_with_delimiter(@property.price) %></div>
              <div class="text-blue-100 text-xs">per month</div>
            </div>
          </div>
        </div>
        
        <!-- Availability Status & Rating -->
        <div class="flex items-center justify-between pt-4 border-t border-gray-100">
          <div class="flex items-center">
            <% if @property.available? %>
              <div class="w-2.5 h-2.5 bg-green-500 rounded-full mr-2"></div>
              <span class="text-green-700 font-medium text-sm">Available now</span>
            <% else %>
              <div class="w-2.5 h-2.5 bg-red-500 rounded-full mr-2"></div>
              <span class="text-red-700 font-medium text-sm">Currently rented</span>
            <% end %>
          </div>
          
          <% if @property.reviews_count > 0 %>
            <div class="flex items-center">
              <div class="flex items-center mr-2">
                <% (1..5).each do |i| %>
                  <svg class="h-4 w-4 <%= i <= @property.average_rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                <% end %>
              </div>
              <span class="text-gray-600 text-sm"><%= @property.average_rating %> (<%= pluralize(@property.reviews_count, 'review') %>)</span>
            </div>
          <% end %>
        </div>
      </div>
      
      <!-- Property Description -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-4">About this place</h2>
        <div class="prose prose-gray max-w-none">
          <p class="text-gray-700 leading-relaxed text-sm"><%= simple_format(@property.description) %></p>
        </div>
      </div>
      
      <!-- Amenities Section -->
      <% if @property.amenities_list.present? %>
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 class="text-xl font-bold text-gray-900 mb-4">What this place offers</h2>
          <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
            <% @property.amenities_list.each do |amenity| %>
              <div class="flex items-center p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div class="w-7 h-7 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <% case amenity %>
                  <% when "Parking" %>
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                  <% when "Pet Friendly" %>
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                    </svg>
                  <% when "Pool" %>
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                  <% when "Gym" %>
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                  <% else %>
                    <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                  <% end %>
                </div>
                <span class="text-gray-700 font-medium text-sm"><%= amenity %></span>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
      
      <!-- Map Section -->
      <%= render 'map' %>
      
      <!-- Reviews Section -->
      <%= render 'reviews_section' %>
      
    </div>
    
    <!-- Sidebar -->
    <div class="lg:col-span-1 space-y-6">
      <%= render 'booking_card' %>
      
      <!-- Host Information - Fixed Layout with Subtle Shadow -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 sticky top-6">
        <h3 class="text-lg font-bold text-gray-900 mb-4">Hosted by</h3>
        <div class="flex items-start space-x-4">
          <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
            <%= (@property.user.name || @property.user.email).first.upcase %>
          </div>
          <div class="flex-1 min-w-0">
            <h4 class="font-semibold text-gray-900 text-sm truncate"><%= @property.user.name || @property.user.email.split('@').first.capitalize %></h4>
            <p class="text-gray-600 text-xs mb-3">Superhost • 5 years hosting</p>
            
            <div class="space-y-2 text-xs text-gray-600">
              <div class="flex items-center">
                <svg class="w-3 h-3 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span>Response rate: 100%</span>
              </div>
              <div class="flex items-center">
                <svg class="w-3 h-3 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>Response time: within an hour</span>
              </div>
            </div>
            
            <% if user_signed_in? && current_user != @property.user %>
              <button class="w-full mt-4 bg-white border border-gray-300 text-gray-700 py-2 px-3 rounded-lg text-sm font-medium hover:border-gray-400 transition-colors">
                Contact Host
              </button>
            <% end %>
          </div>
        </div>
      </div>
      
      <!-- House Rules - Fixed with Subtle Shadow -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100 p-6 sticky top-6">
        <h3 class="text-lg font-bold text-gray-900 mb-4">House Rules</h3>
        <div class="space-y-3">
          <div class="flex items-center justify-between py-1.5">
            <span class="text-gray-700 text-sm">Check-in</span>
            <span class="text-gray-900 font-medium text-sm">3:00 PM - 9:00 PM</span>
          </div>
          <div class="flex items-center justify-between py-1.5">
            <span class="text-gray-700 text-sm">Check-out</span>
            <span class="text-gray-900 font-medium text-sm">11:00 AM</span>
          </div>
          <div class="flex items-center justify-between py-1.5">
            <span class="text-gray-700 text-sm">Maximum guests</span>
            <span class="text-gray-900 font-medium text-sm">4 guests</span>
          </div>
          <hr class="my-3">
          <div class="space-y-2 text-sm text-gray-600">
            <div class="flex items-center">
              <svg class="w-3 h-3 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              <span>No smoking</span>
            </div>
            <div class="flex items-center">
              <svg class="w-3 h-3 mr-2 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
              <span>No pets</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Related Properties Section -->
  <div class="mt-12">
    <div class="flex items-center justify-between mb-6">
      <h2 class="text-2xl font-bold text-gray-900">More places to stay</h2>
      <%= link_to properties_path, class: "text-blue-600 hover:text-blue-700 font-medium text-sm" do %>
        View all
        <svg class="w-4 h-4 inline ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
        </svg>
      <% end %>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <% Property.where.not(id: @property.id).available.limit(4).each do |property| %>
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md transition-all duration-300 group">
          <div class="aspect-[4/3] relative overflow-hidden">
            <% if property.photos.attached? && property.photos.any? %>
              <%= image_tag property.photos.first, 
                            class: "w-full h-full object-cover group-hover:scale-105 transition-transform duration-300", 
                            alt: property.title %>
            <% else %>
              <div class="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
            <% end %>
            
            <% if property.reviews_count > 0 %>
              <div class="absolute top-3 left-3 bg-white bg-opacity-90 backdrop-blur-sm px-2 py-1 rounded-lg flex items-center">
                <svg class="h-3 w-3 text-yellow-400 mr-1" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                </svg>
                <span class="text-xs font-medium text-gray-900"><%= property.average_rating %></span>
              </div>
            <% end %>
          </div>
          
          <div class="p-4">
            <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2 text-sm">
              <%= link_to property.title, property_path(property), class: "hover:text-blue-600 transition-colors" %>
            </h3>
            <p class="text-gray-600 text-xs mb-3"><%= property.city %></p>
            
            <div class="flex items-center justify-between">
              <div class="text-xs text-gray-500">
                <%= property.bedrooms %> bed • <%= property.bathrooms %> bath
              </div>
              <div class="text-right">
                <div class="text-sm font-bold text-gray-900">$<%= number_with_delimiter(property.price) %></div>
                <div class="text-xs text-gray-500">per month</div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  </div>
</div>

<!-- Scheduling Modal -->
<%= render 'scheduling_modal' %>

<!-- Custom Styles -->
<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
  
  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }
  
  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }
</style>