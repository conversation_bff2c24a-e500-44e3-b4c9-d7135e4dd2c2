<% content_for :title, "#{@property.title} - Ofie" %>
<% content_for :meta_description, truncate(@property.description, length: 160) %>

<!-- Enhanced Hero Section with Design System -->
<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
  <div class="absolute top-0 right-1/4 w-96 h-96 bg-gradient-to-l from-purple-300/20 to-pink-300/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-300/20 to-cyan-300/20 rounded-full blur-3xl"></div>

  <div class="relative z-10">
    <!-- Enhanced Image Gallery -->
    <div class="bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/50 border-b border-white/20" data-controller="gallery">
      <!-- Main Image with Glass Morphism -->
      <div class="aspect-[21/9] relative overflow-hidden">
        <% if @property.photos.attached? && @property.photos.any? %>
          <% @property.photos.each_with_index do |image, index| %>
            <div class="<%= index == 0 ? 'block' : 'hidden' %> w-full h-full" data-gallery-target="slide" data-slide-index="<%= index %>">
              <%= image_tag image,
                           class: "w-full h-full object-cover transition-transform duration-700 hover:scale-105",
                           alt: "#{@property.title} - Image #{index + 1}" %>
            </div>
          <% end %>
        <% else %>
          <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
            <div class="text-center text-gray-400">
              <div class="w-20 h-20 bg-gradient-to-r from-gray-300 to-gray-400 rounded-3xl flex items-center justify-center mx-auto mb-4">
                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
              </div>
              <p class="text-lg font-semibold">No images available</p>
            </div>
          </div>
        <% end %>
        
        <!-- Enhanced Floating Action Buttons -->
        <div class="absolute top-6 right-6 flex space-x-3 z-10">
          <% if user_signed_in? %>
            <button class="group w-12 h-12 bg-white/90 backdrop-blur-xl hover:bg-white text-gray-600 hover:text-red-500 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center border border-white/20">
              <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
            </button>
          <% end %>

          <button class="group w-12 h-12 bg-white/90 backdrop-blur-xl hover:bg-white text-gray-600 hover:text-blue-500 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center border border-white/20">
            <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
            </svg>
          </button>

          <button class="group w-12 h-12 bg-white/90 backdrop-blur-xl hover:bg-white text-gray-600 hover:text-emerald-500 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110 flex items-center justify-center border border-white/20">
            <svg class="w-6 h-6 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
          </button>
        </div>

        <!-- Enhanced Navigation Arrows -->
        <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
          <button class="absolute left-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-xl hover:bg-white text-gray-700 hover:text-blue-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center border border-white/20"
                  data-action="click->gallery#previousSlide">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
            </svg>
          </button>

          <button class="absolute right-6 top-1/2 -translate-y-1/2 w-12 h-12 bg-white/90 backdrop-blur-xl hover:bg-white text-gray-700 hover:text-blue-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110 flex items-center justify-center border border-white/20"
                  data-action="click->gallery#nextSlide">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </button>
        <% end %>

        <!-- Enhanced Image Counter -->
        <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
          <div class="absolute bottom-6 right-6 bg-white/90 backdrop-blur-xl text-gray-800 px-4 py-2 rounded-2xl text-sm font-semibold shadow-lg border border-white/20">
            <span data-gallery-target="counter">1</span> / <%= @property.photos.count %>
          </div>
        <% end %>
      </div>
      
      <!-- Enhanced Thumbnail Navigation -->
      <% if @property.photos.attached? && @property.photos.any? && @property.photos.count > 1 %>
        <div class="py-8 bg-white/60 backdrop-blur-sm border-t border-white/20">
          <div class="max-w-6xl mx-auto px-6">
            <div class="flex justify-center space-x-4 overflow-x-auto pb-2">
              <% @property.photos.each_with_index do |image, index| %>
                <button class="flex-shrink-0 w-36 h-28 rounded-2xl overflow-hidden border-3 transition-all duration-300 shadow-lg hover:shadow-xl <%= index == 0 ? 'border-blue-500 shadow-blue-500/25' : 'border-white/50 opacity-70 hover:opacity-100 hover:border-blue-300' %>"
                        data-gallery-target="thumbnail"
                        data-action="click->gallery#goToSlide"
                        data-slide-index="<%= index %>">
                  <%= image_tag image,
                               class: "w-full h-full object-cover transition-transform duration-300 hover:scale-105",
                               alt: "#{@property.title} - Thumbnail #{index + 1}" %>
                </button>
              <% end %>
            </div>
          </div>
        </div>
      <% end %>
    </div>

    <!-- Enhanced Main Content Layout -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <!-- Main Content Column -->
        <div class="lg:col-span-2 space-y-8">
      
          <!-- Enhanced Property Header -->
          <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
            <div class="flex flex-col lg:flex-row lg:items-start lg:justify-between mb-8">
              <div class="flex-1">
                <div class="mb-6">
                  <h1 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent mb-4 leading-tight">
                    <%= @property.title %>
                  </h1>
                  <div class="flex items-center text-gray-600 mb-6">
                    <div class="w-6 h-6 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-3">
                      <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      </svg>
                    </div>
                    <span class="text-lg font-semibold"><%= @property.address %>, <%= @property.city %></span>
                  </div>
                </div>

                <!-- Enhanced Property Stats -->
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                  <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-100">
                    <div class="flex items-center space-x-3">
                      <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                      </div>
                      <div>
                        <div class="text-2xl font-bold text-gray-900"><%= @property.bedrooms %></div>
                        <div class="text-sm font-medium text-gray-600">Bedrooms</div>
                      </div>
                    </div>
                  </div>

                  <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-4 border border-emerald-100">
                    <div class="flex items-center space-x-3">
                      <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11"></path>
                        </svg>
                      </div>
                      <div>
                        <div class="text-2xl font-bold text-gray-900"><%= @property.bathrooms %></div>
                        <div class="text-sm font-medium text-gray-600">Bathrooms</div>
                      </div>
                    </div>
                  </div>

                  <% if @property.square_feet.present? %>
                    <div class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-4 border border-purple-100">
                      <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
                          <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                          </svg>
                        </div>
                        <div>
                          <div class="text-2xl font-bold text-gray-900"><%= number_with_delimiter(@property.square_feet) %></div>
                          <div class="text-sm font-medium text-gray-600">Sq Ft</div>
                        </div>
                      </div>
                    </div>
                  <% end %>
                </div>
              </div>

              <!-- Enhanced Price Badge -->
              <div class="mt-6 lg:mt-0 lg:ml-8">
                <div class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-6 rounded-3xl text-center shadow-xl shadow-blue-500/25 border border-white/20">
                  <div class="text-4xl font-bold mb-2">$<%= number_with_delimiter(@property.price) %></div>
                  <div class="text-blue-100 text-lg font-medium">per month</div>
                </div>
              </div>
            </div>
        
            <!-- Enhanced Availability Status & Rating -->
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between pt-6 border-t border-gray-200/50 space-y-4 sm:space-y-0">
              <div class="flex items-center space-x-4">
                <% if @property.available? %>
                  <div class="inline-flex items-center bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 px-4 py-2 rounded-2xl border border-green-200">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                    <span class="font-bold text-sm">Available Now</span>
                  </div>
                <% else %>
                  <div class="inline-flex items-center bg-gradient-to-r from-red-100 to-pink-100 text-red-800 px-4 py-2 rounded-2xl border border-red-200">
                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span class="font-bold text-sm">Currently Rented</span>
                  </div>
                <% end %>

                <div class="inline-flex items-center bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 px-4 py-2 rounded-2xl border border-blue-200">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span class="font-bold text-sm">Verified Listing</span>
                </div>
              </div>

              <% if @property.reviews_count > 0 %>
                <div class="flex items-center bg-gradient-to-r from-amber-50 to-yellow-50 px-4 py-2 rounded-2xl border border-amber-200">
                  <div class="flex items-center mr-3">
                    <% (1..5).each do |i| %>
                      <svg class="h-5 w-5 <%= i <= @property.average_rating ? 'text-yellow-400' : 'text-gray-300' %>" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    <% end %>
                  </div>
                  <span class="text-amber-800 font-bold text-sm"><%= @property.average_rating %> (<%= pluralize(@property.reviews_count, 'review') %>)</span>
                </div>
              <% end %>
            </div>
          </div>
      
          <!-- Enhanced Property Description -->
          <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
            <div class="flex items-center space-x-3 mb-6">
              <div class="w-10 h-10 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <h2 class="text-3xl font-bold text-gray-900">About This Place</h2>
            </div>
            <div class="prose prose-lg prose-gray max-w-none">
              <p class="text-gray-700 leading-relaxed text-lg"><%= simple_format(@property.description) %></p>
            </div>
          </div>

          <!-- Enhanced Amenities Section -->
          <% if @property.amenities_list.present? %>
            <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
              <div class="flex items-center space-x-3 mb-8">
                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                  </svg>
                </div>
                <h2 class="text-3xl font-bold text-gray-900">What This Place Offers</h2>
              </div>
              <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                <% @property.amenities_list.each do |amenity| %>
                  <div class="group flex items-center p-4 bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 border border-gray-200 hover:border-blue-300 hover:shadow-lg">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-200">
                      <% case amenity %>
                      <% when "Parking" %>
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                      <% when "Pet Friendly" %>
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                      <% when "Pool" %>
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                      <% when "Gym" %>
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                      <% else %>
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                      <% end %>
                    </div>
                    <span class="text-gray-800 font-semibold"><%= amenity %></span>
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>
      
      <!-- Map Section -->
      <%= render 'map' %>
      
      <!-- Reviews Section -->
      <%= render 'reviews_section' %>

      <!-- Comments Section -->
      <%= render 'property_comments/comments_section' %>

    </div>
    
    <!-- Sidebar -->
    <div class="lg:col-span-1 space-y-6">
      <%= render 'booking_card' %>
      
      <!-- Enhanced Host Information -->
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 sticky top-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900">Hosted by</h3>
        </div>

        <div class="flex items-start space-x-6">
          <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center text-white font-bold text-2xl flex-shrink-0 shadow-lg">
            <%= (@property.user.name || @property.user.email).first.upcase %>
          </div>
          <div class="flex-1 min-w-0">
            <h4 class="font-bold text-gray-900 text-lg mb-2"><%= @property.user.name || @property.user.email.split('@').first.capitalize %></h4>
            <div class="inline-flex items-center bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 px-3 py-1 rounded-2xl border border-amber-200 mb-4">
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
              </svg>
              <span class="font-bold text-sm">Superhost • 5 years hosting</span>
            </div>

            <div class="space-y-3 mb-6">
              <div class="flex items-center bg-gradient-to-r from-green-50 to-emerald-50 px-3 py-2 rounded-2xl border border-green-200">
                <svg class="w-4 h-4 mr-3 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <span class="text-green-800 font-semibold text-sm">Response rate: 100%</span>
              </div>
              <div class="flex items-center bg-gradient-to-r from-blue-50 to-indigo-50 px-3 py-2 rounded-2xl border border-blue-200">
                <svg class="w-4 h-4 mr-3 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-blue-800 font-semibold text-sm">Response time: within an hour</span>
              </div>
            </div>

            <% if user_signed_in? && current_user != @property.user %>
              <button class="group w-full bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white py-3 px-6 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 flex items-center justify-center">
                <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                Contact Host
              </button>
            <% end %>
          </div>
        </div>
      </div>
      
      <!-- Enhanced House Rules -->
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 sticky top-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-10 h-10 bg-gradient-to-r from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900">House Rules</h3>
        </div>

        <div class="space-y-4">
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-4 border border-blue-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                  </svg>
                </div>
                <span class="text-blue-800 font-semibold">Check-in</span>
              </div>
              <span class="text-blue-900 font-bold">3:00 PM - 9:00 PM</span>
            </div>
          </div>

          <div class="bg-gradient-to-r from-emerald-50 to-teal-50 rounded-2xl p-4 border border-emerald-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-xl flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                  </svg>
                </div>
                <span class="text-emerald-800 font-semibold">Check-out</span>
              </div>
              <span class="text-emerald-900 font-bold">11:00 AM</span>
            </div>
          </div>

          <div class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-2xl p-4 border border-purple-200">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                  </svg>
                </div>
                <span class="text-purple-800 font-semibold">Maximum guests</span>
              </div>
              <span class="text-purple-900 font-bold">4 guests</span>
            </div>
          </div>

          <div class="border-t border-gray-200 pt-4 mt-6">
            <div class="space-y-3">
              <div class="flex items-center bg-gradient-to-r from-red-50 to-pink-50 px-4 py-3 rounded-2xl border border-red-200">
                <div class="w-6 h-6 bg-gradient-to-r from-red-500 to-pink-600 rounded-xl flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </div>
                <span class="text-red-800 font-semibold">No smoking</span>
              </div>
              <div class="flex items-center bg-gradient-to-r from-red-50 to-pink-50 px-4 py-3 rounded-2xl border border-red-200">
                <div class="w-6 h-6 bg-gradient-to-r from-red-500 to-pink-600 rounded-xl flex items-center justify-center mr-3">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                </div>
                <span class="text-red-800 font-semibold">No pets</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Enhanced Related Properties Section -->
  <div class="mt-16">
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
      <div class="flex items-center justify-between mb-8">
        <div class="flex items-center space-x-3">
          <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
          <h2 class="text-3xl font-bold text-gray-900">More Places to Stay</h2>
        </div>
        <%= link_to properties_path, class: "group inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
          View All Properties
          <svg class="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
          </svg>
        <% end %>
      </div>
    
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <% Property.where.not(id: @property.id).available.limit(4).each do |property| %>
          <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl hover:-translate-y-1 transition-all duration-300">
            <div class="aspect-[4/3] relative overflow-hidden">
              <% if property.photos.attached? && property.photos.any? %>
                <%= image_tag property.photos.first,
                              class: "w-full h-full object-cover group-hover:scale-105 transition-transform duration-500",
                              alt: property.title %>
              <% else %>
                <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                  <div class="w-12 h-12 bg-gradient-to-r from-gray-300 to-gray-400 rounded-2xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                  </div>
                </div>
              <% end %>

              <% if property.reviews_count > 0 %>
                <div class="absolute top-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-2xl flex items-center shadow-lg border border-white/20">
                  <svg class="h-4 w-4 text-yellow-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  <span class="text-sm font-bold text-gray-900"><%= property.average_rating %></span>
                </div>
              <% end %>

              <div class="absolute top-4 right-4 bg-gradient-to-r from-green-500 to-emerald-600 text-white px-3 py-1 rounded-2xl text-xs font-bold shadow-lg">
                Available
              </div>
            </div>

            <div class="p-6">
              <h3 class="font-bold text-gray-900 mb-3 line-clamp-2 text-lg group-hover:text-blue-600 transition-colors duration-200">
                <%= link_to property.title, property_path(property) %>
              </h3>
              <div class="flex items-center text-gray-600 mb-4">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                </svg>
                <span class="text-sm font-medium"><%= property.city %></span>
              </div>

              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                    </svg>
                    <span class="font-semibold"><%= property.bedrooms %></span>
                  </div>
                  <div class="flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10v11M20 10v11"></path>
                    </svg>
                    <span class="font-semibold"><%= property.bathrooms %></span>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-xl font-bold text-gray-900">$<%= number_with_delimiter(property.price) %></div>
                  <div class="text-sm text-gray-500 font-medium">per month</div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    </div>
  </div>
</div>

<!-- Scheduling Modal -->
<%= render 'scheduling_modal' %>

<!-- Enhanced Custom Styles -->
<style>
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }

  .backdrop-blur-xl {
    backdrop-filter: blur(24px);
  }

  .sticky {
    position: -webkit-sticky;
    position: sticky;
  }

  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }

  /* Enhanced animations */
  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-slide-up {
    animation: slideInUp 0.6s ease-out;
  }

  /* Accessibility improvements */
  @media (prefers-reduced-motion: reduce) {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
</style>