<% content_for :title, "Help Center - Ofie Knowledge Base" %>
<% content_for :description, "Comprehensive help center with guides, tutorials, and answers for landlords and tenants using the Ofie rental platform." %>

<!-- Hero Section -->
<div class="relative min-h-screen bg-gradient-to-br from-slate-900 via-emerald-900 to-teal-900 overflow-hidden">
  <!-- Background Effects -->
  <div class="absolute inset-0 bg-gradient-to-br from-black/30 via-transparent to-black/20"></div>
  <div class="absolute top-0 right-1/4 w-96 h-96 bg-gradient-to-l from-emerald-400/20 to-teal-500/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-1/4 w-96 h-96 bg-gradient-to-r from-green-400/20 to-emerald-500/20 rounded-full blur-3xl"></div>
  
  <!-- Floating Elements -->
  <div class="absolute top-20 left-10 w-32 h-32 bg-white/5 rounded-full blur-2xl animate-pulse"></div>
  <div class="absolute bottom-40 right-20 w-48 h-48 bg-emerald-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 2s;"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-32">
    <div class="text-center text-white mb-16">
      <div class="mb-8">
        <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-3xl mb-8 shadow-2xl shadow-emerald-500/25">
          <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
          </svg>
        </div>
        <h1 class="text-6xl md:text-7xl font-bold mb-6 leading-tight">
          Knowledge <span class="bg-gradient-to-r from-emerald-400 via-teal-400 to-green-400 bg-clip-text text-transparent">Center</span>
        </h1>
        <p class="text-2xl md:text-3xl text-emerald-100 mb-8 max-w-4xl mx-auto leading-relaxed">
          Everything you need to know about using Ofie effectively
        </p>
      </div>
      
      <!-- Search Bar -->
      <div class="bg-white/10 backdrop-blur-xl rounded-3xl border border-white/20 p-8 max-w-4xl mx-auto mb-12">
        <div class="relative">
          <div class="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
            <svg class="h-6 w-6 text-white/70" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input type="text" 
                 placeholder="Search for help articles, guides, and tutorials..."
                 class="w-full pl-16 pr-6 py-5 text-lg font-medium border-0 rounded-2xl bg-white/20 backdrop-blur-sm text-white placeholder-white/70 focus:ring-2 focus:ring-emerald-500 focus:bg-white/30 transition-all duration-300"
                 data-controller="search"
                 data-search-target="input"
                 data-action="input->search#search">
          <div class="absolute inset-y-0 right-0 pr-6 flex items-center">
            <kbd class="hidden sm:inline-flex items-center px-3 py-1 border border-white/30 rounded-lg text-sm text-white/70">⌘K</kbd>
          </div>
        </div>
      </div>
      
      <!-- Quick Stats -->
      <div class="flex flex-wrap items-center justify-center gap-8 text-emerald-200">
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
          </div>
          <span class="font-bold text-lg">150+ Articles</span>
        </div>
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
            </svg>
          </div>
          <span class="font-bold text-lg">50+ Videos</span>
        </div>
        <div class="flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-2xl px-6 py-3 border border-white/20">
          <div class="w-10 h-10 bg-gradient-to-r from-purple-400 to-indigo-500 rounded-xl flex items-center justify-center">
            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
          </div>
          <span class="font-bold text-lg">24/7 Updated</span>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Main Content -->
<div class="bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50 relative">
  <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
  
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
    
    <!-- Popular Topics -->
    <div class="mb-20">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6">
          Popular Topics
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Quick access to the most searched help topics and frequently needed guides
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <!-- Getting Started -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-blue-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Getting Started</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Learn the basics of using Ofie, from account setup to your first rental transaction.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Creating your account
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Profile setup guide
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Platform overview
            </a>
          </div>
        </div>

        <!-- For Landlords -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-emerald-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">For Landlords</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Everything landlords need to know about property management and tenant relations.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Listing your property
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Tenant screening
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Lease management
            </a>
          </div>
        </div>

        <!-- For Tenants -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-purple-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">For Tenants</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Guides for tenants on finding properties, applications, and rental processes.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Searching for properties
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Rental applications
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Making payments
            </a>
          </div>
        </div>

        <!-- Payments & Billing -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-amber-500 to-orange-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-amber-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Payments & Billing</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Understanding payment processing, fees, and financial management features.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Payment methods
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Setting up auto-pay
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Understanding fees
            </a>
          </div>
        </div>

        <!-- Technical Support -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-red-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Technical Support</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Troubleshooting common issues and technical questions about the platform.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Login issues
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Browser compatibility
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Mobile app help
            </a>
          </div>
        </div>

        <!-- Legal & Compliance -->
        <div class="group bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
          <div class="w-16 h-16 bg-gradient-to-r from-gray-500 to-slate-600 rounded-2xl flex items-center justify-center mb-6 shadow-lg shadow-gray-500/25 group-hover:scale-110 transition-transform duration-300">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
            </svg>
          </div>
          <h3 class="text-2xl font-bold text-gray-900 mb-4">Legal & Compliance</h3>
          <p class="text-gray-600 mb-6 leading-relaxed">
            Information about legal requirements, compliance, and platform policies.
          </p>
          <div class="space-y-3">
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Terms of service
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Privacy policy
            </a>
            <a href="#" class="block text-emerald-600 hover:text-emerald-700 font-semibold transition-colors duration-200">
              → Fair housing compliance
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Featured Guides -->
    <div class="mb-20">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6">
          Featured Guides
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Step-by-step tutorials to help you master the Ofie platform
        </p>
      </div>
      
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <!-- Guide 1 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="relative h-48 bg-gradient-to-br from-blue-100 to-cyan-100">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-24 h-24 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-blue-500/25">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
              </div>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-blue-500 text-white px-3 py-1 rounded-xl text-sm font-bold">NEW</span>
            </div>
          </div>
          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Complete Landlord Setup Guide</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              A comprehensive walkthrough for new landlords, covering everything from account creation 
              to your first successful rental.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                15 min read
              </div>
              <a href="#" class="bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                Start Guide
              </a>
            </div>
          </div>
        </div>

        <!-- Guide 2 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="relative h-48 bg-gradient-to-br from-emerald-100 to-teal-100">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-24 h-24 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-emerald-500/25">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
              </div>
            </div>
            <div class="absolute top-4 right-4">
              <span class="bg-emerald-500 text-white px-3 py-1 rounded-xl text-sm font-bold">POPULAR</span>
            </div>
          </div>
          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Tenant's Property Search Guide</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              Learn how to effectively search for properties, use filters, and submit winning 
              rental applications that get noticed.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                10 min read
              </div>
              <a href="#" class="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                Start Guide
              </a>
            </div>
          </div>
        </div>

        <!-- Guide 3 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="relative h-48 bg-gradient-to-br from-purple-100 to-indigo-100">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-24 h-24 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-purple-500/25">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                </svg>
              </div>
            </div>
          </div>
          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Payment & Billing Master Class</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              Everything about payments: setting up auto-pay, understanding fees, handling disputes, 
              and maximizing financial efficiency.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                20 min read
              </div>
              <a href="#" class="bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                Start Guide
              </a>
            </div>
          </div>
        </div>

        <!-- Guide 4 -->
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 overflow-hidden hover:shadow-2xl transition-all duration-500">
          <div class="relative h-48 bg-gradient-to-br from-orange-100 to-red-100">
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="w-24 h-24 bg-gradient-to-r from-orange-500 to-red-600 rounded-3xl flex items-center justify-center shadow-2xl shadow-orange-500/25">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
              </div>
            </div>
          </div>
          <div class="p-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">Analytics & Reporting Deep Dive</h3>
            <p class="text-gray-600 mb-6 leading-relaxed">
              Unlock the power of data with comprehensive guides on using analytics, generating reports, 
              and making data-driven decisions.
            </p>
            <div class="flex items-center justify-between">
              <div class="flex items-center text-sm text-gray-500">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                25 min read
              </div>
              <a href="#" class="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                Start Guide
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Video Tutorials -->
    <div class="mb-20">
      <div class="text-center mb-16">
        <h2 class="text-4xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent mb-6">
          Video Tutorials
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
          Watch and learn with our comprehensive video library
        </p>
      </div>
      
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-12">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Featured Video -->
          <div class="lg:col-span-2">
            <div class="relative bg-gradient-to-br from-gray-900 to-gray-700 rounded-2xl overflow-hidden shadow-2xl">
              <div class="aspect-video flex items-center justify-center">
                <button class="group w-20 h-20 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center backdrop-blur-sm transition-all duration-300 transform hover:scale-110">
                  <svg class="w-8 h-8 text-white ml-1 group-hover:scale-110 transition-transform duration-200" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                  </svg>
                </button>
              </div>
              <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-6">
                <h3 class="text-xl font-bold text-white mb-2">Ofie Platform Overview</h3>
                <p class="text-gray-300">Complete walkthrough of all platform features</p>
                <div class="flex items-center mt-3 text-sm text-gray-400">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  12:34
                </div>
              </div>
            </div>
          </div>
          
          <!-- Video List -->
          <div class="space-y-4">
            <h4 class="text-xl font-bold text-gray-900 mb-4">Recently Added</h4>
            
            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
              <div class="relative flex-shrink-0">
                <div class="w-16 h-12 bg-gradient-to-br from-blue-500 to-cyan-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="absolute -bottom-1 -right-1 bg-white text-xs font-bold px-2 py-1 rounded text-gray-600">5:23</span>
              </div>
              <div class="flex-1 min-w-0">
                <h5 class="font-semibold text-gray-900 truncate">Property Listing Best Practices</h5>
                <p class="text-sm text-gray-600">How to create listings that attract quality tenants</p>
              </div>
            </div>

            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
              <div class="relative flex-shrink-0">
                <div class="w-16 h-12 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="absolute -bottom-1 -right-1 bg-white text-xs font-bold px-2 py-1 rounded text-gray-600">8:17</span>
              </div>
              <div class="flex-1 min-w-0">
                <h5 class="font-semibold text-gray-900 truncate">Tenant Screening Process</h5>
                <p class="text-sm text-gray-600">Step-by-step guide to screening applicants</p>
              </div>
            </div>

            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200 cursor-pointer">
              <div class="relative flex-shrink-0">
                <div class="w-16 h-12 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-lg flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <span class="absolute -bottom-1 -right-1 bg-white text-xs font-bold px-2 py-1 rounded text-gray-600">6:45</span>
              </div>
              <div class="flex-1 min-w-0">
                <h5 class="font-semibold text-gray-900 truncate">Mobile App Tour</h5>
                <p class="text-sm text-gray-600">Managing your rentals on the go</p>
              </div>
            </div>

            <div class="pt-4">
              <a href="#" class="block text-center bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 shadow-lg hover:shadow-xl">
                View All Videos
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Still Need Help -->
    <div class="text-center">
      <div class="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-3xl shadow-2xl shadow-emerald-500/25 p-12 text-white">
        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-6">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
          </svg>
        </div>
        <h2 class="text-4xl font-bold mb-6">Still Need Help?</h2>
        <p class="text-xl text-emerald-100 mb-8 max-w-3xl mx-auto">
          Can't find what you're looking for? Our support team is here to help with personalized assistance.
        </p>
        <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
          <%= link_to "Contact Support", contact_support_path, 
                      class: "bg-white text-emerald-600 hover:bg-emerald-50 px-8 py-4 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
          <button class="border-2 border-white text-white hover:bg-white hover:text-emerald-600 px-8 py-4 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5"
                  data-action="click->chat#open">
            Start Live Chat
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Stimulus Controllers -->
<script>
// Search Controller
class SearchController extends Controller {
  static targets = ["input"]
  
  connect() {
    // Initialize search functionality
    this.setupKeyboardShortcuts()
  }
  
  search(event) {
    const query = event.target.value
    if (query.length > 2) {
      this.performSearch(query)
    }
  }
  
  setupKeyboardShortcuts() {
    document.addEventListener('keydown', (event) => {
      if ((event.metaKey || event.ctrlKey) && event.key === 'k') {
        event.preventDefault()
        this.inputTarget.focus()
      }
    })
  }
  
  performSearch(query) {
    // Implement search functionality
    console.log('Searching for:', query)
    // This would integrate with your search backend
  }
}

// Chat Controller (reused from contact page)
class ChatController extends Controller {
  open() {
    console.log('Opening live chat...')
    // Integration with chat service
  }
}
</script>

<style>
  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }
</style>