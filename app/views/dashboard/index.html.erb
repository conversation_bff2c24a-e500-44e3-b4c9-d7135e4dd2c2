<% content_for :page_title, "Dashboard" %>

<div class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
  <!-- Background Pattern -->
  <div class="absolute inset-0 bg-grid-slate-100/50 [mask-image:linear-gradient(0deg,transparent,black)] pointer-events-none"></div>
  <div class="absolute top-0 right-0 w-96 h-96 bg-gradient-to-l from-purple-300/20 to-pink-300/20 rounded-full blur-3xl"></div>
  <div class="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-r from-blue-300/20 to-cyan-300/20 rounded-full blur-3xl"></div>
  
  <!-- Enhanced Dashboard Header -->
  <div class="relative z-10 bg-white/80 backdrop-blur-xl shadow-xl shadow-gray-200/20 border-b border-white/20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="py-8">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
          <div class="mb-6 lg:mb-0">
            <div class="flex items-center mb-4">
              <div class="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl mr-4 shadow-lg shadow-blue-500/25">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v6m8-6v6m-8-2h8"></path>
                </svg>
              </div>
              <div>
                <h1 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-gray-800 to-gray-700 bg-clip-text text-transparent">
                  Welcome back, <%= current_user.name || current_user.email.split('@').first.capitalize %>!
                </h1>
                <p class="mt-2 text-lg text-gray-600 font-medium">
                  <% if current_user.landlord? %>
                    Manage your properties and track your rental business
                  <% else %>
                    Track your applications, payments, and favorite properties
                  <% end %>
                </p>
              </div>
            </div>
            <div class="flex items-center space-x-4 text-sm text-gray-500">
              <div class="flex items-center">
                <svg class="w-4 h-4 mr-1 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                Last updated: <%= Time.current.strftime('%B %d, %Y at %I:%M %p') %>
              </div>
              <span class="h-4 w-px bg-gray-300"></span>
              <div class="flex items-center">
                <div class="w-2 h-2 bg-green-500 rounded-full mr-1 animate-pulse"></div>
                Real-time sync enabled
              </div>
            </div>
          </div>
          
          <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            <% if current_user.landlord? %>
              <%= link_to new_property_path, class: "group inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-semibold rounded-2xl shadow-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-xl hover:shadow-blue-500/25" do %>
                <svg class="-ml-1 mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Add Property
              <% end %>
              <%= link_to dashboard_analytics_path, class: "group inline-flex items-center justify-center px-6 py-3 border-2 border-gray-200 text-sm font-semibold rounded-2xl text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg" do %>
                <svg class="-ml-1 mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Analytics
              <% end %>
            <% else %>
              <%= link_to properties_path, class: "group inline-flex items-center justify-center px-6 py-3 border border-transparent text-sm font-semibold rounded-2xl shadow-lg text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-xl hover:shadow-blue-500/25" do %>
                <svg class="-ml-1 mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                Browse Properties
              <% end %>
              <%= link_to conversations_path, class: "group inline-flex items-center justify-center px-6 py-3 border-2 border-gray-200 text-sm font-semibold rounded-2xl text-gray-700 bg-white/80 backdrop-blur-sm hover:bg-white hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-all duration-300 transform hover:-translate-y-0.5 hover:shadow-lg" do %>
                <svg class="-ml-1 mr-2 h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                </svg>
                Messages
              <% end %>
            <% end %>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Enhanced Dashboard Content -->
  <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <% if current_user.landlord? %>
      <%= render 'landlord_dashboard' %>
    <% else %>
      <%= render 'tenant_dashboard' %>
    <% end %>
  </div>
</div>

<!-- Enhanced Dashboard JavaScript -->
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Animate stats counters
    function animateCounters() {
      const counters = document.querySelectorAll('[data-stat]');
      counters.forEach(counter => {
        const target = parseInt(counter.textContent.replace(/[,$]/g, ''));
        if (target > 0) {
          let count = 0;
          const increment = target / 50;
          const timer = setInterval(() => {
            count += increment;
            if (count >= target) {
              counter.textContent = target.toLocaleString();
              clearInterval(timer);
            } else {
              counter.textContent = Math.ceil(count).toLocaleString();
            }
          }, 20);
        }
      });
    }

    // Auto-refresh dashboard data every 5 minutes
    setInterval(function() {
      if (document.visibilityState === 'visible') {
        fetch(window.location.href + '.json')
          .then(response => response.json())
          .then(data => {
            updateDashboardStats(data.stats);
          })
          .catch(error => console.log('Dashboard refresh failed:', error));
      }
    }, 300000); // 5 minutes

    function updateDashboardStats(stats) {
      Object.keys(stats).forEach(key => {
        const element = document.querySelector(`[data-stat="${key}"]`);
        if (element) {
          element.textContent = stats[key];
        }
      });
    }

    // Add loading states to action buttons
    const actionButtons = document.querySelectorAll('a[href^="/"], button');
    actionButtons.forEach(button => {
      button.addEventListener('click', function() {
        if (!this.dataset.noLoading) {
          this.style.opacity = '0.7';
          this.style.pointerEvents = 'none';
          setTimeout(() => {
            this.style.opacity = '1';
            this.style.pointerEvents = 'auto';
          }, 2000);
        }
      });
    });

    // Animate on scroll
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = '1';
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, observerOptions);

    document.querySelectorAll('.animate-on-scroll').forEach(el => {
      el.style.opacity = '0';
      el.style.transform = 'translateY(20px)';
      el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
      observer.observe(el);
    });

    // Run counter animation after a short delay
    setTimeout(animateCounters, 500);
  });
</script>

<style>
  .bg-grid-slate-100\/50 {
    background-image: url("data:image/svg+xml,%3csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3e%3cg fill='%23f1f5f9' fill-opacity='0.3' fill-rule='evenodd'%3e%3cpath d='m0 40 40-40h-40v40zm40 0v-40h-40z'/%3e%3c/g%3e%3c/svg%3e");
  }
  
  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
</style>
