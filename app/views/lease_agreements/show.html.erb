<% content_for :title, "Lease Agreement" %>
<% content_for :description, "View lease agreement details" %>

<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Header -->
    <div class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">Lease Agreement</h1>
          <p class="text-gray-600">Lease agreement details and status</p>
        </div>
        
        <div class="flex items-center space-x-4">
          <%= link_to lease_agreements_path, 
                      class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Leases
          <% end %>
        </div>
      </div>
    </div>

    <!-- Status Banner -->
    <div class="mb-8">
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 bg-gradient-to-r <%= 
              case @lease_agreement.status
              when 'draft' then 'from-gray-100 to-gray-200'
              when 'pending_signatures' then 'from-amber-100 to-orange-100'
              when 'signed' then 'from-blue-100 to-indigo-100'
              when 'active' then 'from-green-100 to-emerald-100'
              when 'terminated' then 'from-red-100 to-pink-100'
              else 'from-gray-100 to-gray-200'
              end %> rounded-2xl flex items-center justify-center">
              <svg class="w-8 h-8 <%= 
                case @lease_agreement.status
                when 'draft' then 'text-gray-600'
                when 'pending_signatures' then 'text-amber-600'
                when 'signed' then 'text-blue-600'
                when 'active' then 'text-green-600'
                when 'terminated' then 'text-red-600'
                else 'text-gray-600'
                end %>" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <% case @lease_agreement.status %>
                <% when 'draft' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                <% when 'pending_signatures' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                <% when 'signed' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <% when 'active' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
                <% when 'terminated' %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <% else %>
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                <% end %>
              </svg>
            </div>
            
            <div>
              <h2 class="text-2xl font-bold text-gray-900">
                Lease <%= @lease_agreement.status.humanize %>
              </h2>
              <p class="text-gray-600">
                <% if @lease_agreement.lease_number.present? %>
                  Lease #<%= @lease_agreement.lease_number %>
                <% end %>
                Created on <%= @lease_agreement.created_at.strftime("%B %d, %Y") %>
              </p>
            </div>
          </div>
          
          <div class="text-right">
            <span class="inline-flex items-center px-4 py-2 rounded-2xl text-sm font-bold <%= 
              case @lease_agreement.status
              when 'draft' then 'bg-gray-100 text-gray-800'
              when 'pending_signatures' then 'bg-amber-100 text-amber-800'
              when 'signed' then 'bg-blue-100 text-blue-800'
              when 'active' then 'bg-green-100 text-green-800'
              when 'terminated' then 'bg-red-100 text-red-800'
              else 'bg-gray-100 text-gray-800'
              end %>">
              <%= @lease_agreement.status.humanize %>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Property Information -->
    <div class="mb-8">
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Property Information</h3>
        
        <div class="flex items-center space-x-4">
          <% if @property.photos.attached? && @property.photos.any? %>
            <div class="w-20 h-20 rounded-2xl overflow-hidden flex-shrink-0">
              <%= image_tag @property.photos.first, 
                            class: "w-full h-full object-cover",
                            alt: @property.title %>
            </div>
          <% else %>
            <div class="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-100 rounded-2xl flex items-center justify-center flex-shrink-0">
              <svg class="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
              </svg>
            </div>
          <% end %>
          
          <div class="flex-1">
            <h4 class="text-lg font-bold text-gray-900">
              <%= link_to @property.title, property_path(@property), class: "hover:text-blue-600 transition-colors" %>
            </h4>
            <p class="text-gray-600"><%= @property.address %></p>
          </div>
        </div>
      </div>
    </div>

    <!-- Lease Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
      <!-- Lease Terms -->
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Lease Terms</h3>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-bold text-gray-700 mb-1">Lease Period</label>
            <p class="text-gray-900">
              <%= @lease_agreement.lease_start_date.strftime("%B %d, %Y") %> - 
              <%= @lease_agreement.lease_end_date.strftime("%B %d, %Y") %>
            </p>
            <p class="text-sm text-gray-500">
              <%= distance_of_time_in_words(@lease_agreement.lease_start_date, @lease_agreement.lease_end_date) %>
            </p>
          </div>
          
          <div>
            <label class="block text-sm font-bold text-gray-700 mb-1">Monthly Rent</label>
            <p class="text-2xl font-bold text-blue-600"><%= number_to_currency(@lease_agreement.monthly_rent) %></p>
          </div>
          
          <div>
            <label class="block text-sm font-bold text-gray-700 mb-1">Security Deposit</label>
            <p class="text-lg font-bold text-gray-900"><%= number_to_currency(@lease_agreement.security_deposit_amount) %></p>
          </div>
        </div>
      </div>

      <!-- Parties & Signatures -->
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
        <h3 class="text-xl font-bold text-gray-900 mb-4">Parties & Signatures</h3>
        
        <div class="space-y-4">
          <!-- Landlord -->
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-2xl">
            <div>
              <p class="font-bold text-gray-900">Landlord</p>
              <p class="text-gray-600"><%= @lease_agreement.landlord.name || @lease_agreement.landlord.email %></p>
            </div>
            <div class="text-right">
              <% if @lease_agreement.landlord_signed_at.present? %>
                <span class="inline-flex items-center px-2 py-1 rounded-xl text-xs font-bold bg-green-100 text-green-800">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Signed
                </span>
                <p class="text-xs text-gray-500 mt-1">
                  <%= @lease_agreement.landlord_signed_at.strftime("%m/%d/%Y") %>
                </p>
              <% else %>
                <span class="inline-flex items-center px-2 py-1 rounded-xl text-xs font-bold bg-amber-100 text-amber-800">
                  Pending
                </span>
              <% end %>
            </div>
          </div>
          
          <!-- Tenant -->
          <div class="flex items-center justify-between p-3 bg-gray-50 rounded-2xl">
            <div>
              <p class="font-bold text-gray-900">Tenant</p>
              <p class="text-gray-600"><%= @lease_agreement.tenant.name || @lease_agreement.tenant.email %></p>
            </div>
            <div class="text-right">
              <% if @lease_agreement.tenant_signed_at.present? %>
                <span class="inline-flex items-center px-2 py-1 rounded-xl text-xs font-bold bg-green-100 text-green-800">
                  <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Signed
                </span>
                <p class="text-xs text-gray-500 mt-1">
                  <%= @lease_agreement.tenant_signed_at.strftime("%m/%d/%Y") %>
                </p>
              <% else %>
                <span class="inline-flex items-center px-2 py-1 rounded-xl text-xs font-bold bg-amber-100 text-amber-800">
                  Pending
                </span>
              <% end %>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Terms and Conditions -->
    <% if @lease_agreement.terms_and_conditions.present? %>
      <div class="mb-8">
        <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-6">
          <h3 class="text-xl font-bold text-gray-900 mb-4">Terms and Conditions</h3>
          <div class="prose max-w-none">
            <p class="text-gray-900 whitespace-pre-line"><%= @lease_agreement.terms_and_conditions %></p>
          </div>
        </div>
      </div>
    <% end %>

    <!-- Action Buttons -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <% if @can_manage %>
          <!-- Landlord Actions -->
          <% if @lease_agreement.status == 'draft' %>
            <%= link_to edit_lease_agreement_path(@lease_agreement), 
                        class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Edit Lease
            <% end %>
          <% end %>
          
          <% if @lease_agreement.status == 'signed' %>
            <%= link_to activate_lease_agreement_path(@lease_agreement), 
                        method: :post,
                        class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",
                        data: { confirm: "Activate this lease agreement?" } do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
              </svg>
              Activate Lease
            <% end %>
          <% end %>
        <% end %>
        
        <!-- Signing Actions -->
        <% if @can_sign %>
          <% if current_user == @lease_agreement.tenant && @lease_agreement.tenant_signed_at.blank? %>
            <%= link_to sign_tenant_lease_agreement_path(@lease_agreement), 
                        method: :post,
                        class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",
                        data: { confirm: "Sign this lease agreement?" } do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
              </svg>
              Sign as Tenant
            <% end %>
          <% end %>
          
          <% if current_user == @lease_agreement.landlord && @lease_agreement.landlord_signed_at.blank? %>
            <%= link_to sign_landlord_lease_agreement_path(@lease_agreement), 
                        method: :post,
                        class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5",
                        data: { confirm: "Sign this lease agreement?" } do %>
              <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
              </svg>
              Sign as Landlord
            <% end %>
          <% end %>
        <% end %>
      </div>
      
      <div>
        <%= link_to property_path(@property), 
                    class: "inline-flex items-center px-6 py-3 border-2 border-gray-200 text-gray-700 bg-white hover:bg-gray-50 hover:border-gray-300 rounded-2xl font-bold transition-all duration-300 transform hover:-translate-y-0.5" do %>
          <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"></path>
          </svg>
          View Property
        <% end %>
      </div>
    </div>
  </div>
</div>
