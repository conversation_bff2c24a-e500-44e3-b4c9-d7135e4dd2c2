<div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200">
  <div class="flex items-start space-x-3">
    <!-- User Avatar -->
    <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
      <%= (reply.user.name || reply.user.email).first.upcase %>
    </div>
    
    <!-- Reply Content -->
    <div class="flex-1 min-w-0">
      <!-- User Info and Timestamp -->
      <div class="flex items-center justify-between mb-2">
        <div class="flex items-center space-x-2">
          <h5 class="font-semibold text-gray-900 text-sm">
            <%= reply.user.name || reply.user.email.split('@').first.capitalize %>
          </h5>
          <% if reply.user == @property.user %>
            <span class="inline-flex items-center px-2 py-0.5 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 text-xs font-bold rounded-lg border border-blue-200">
              <svg class="w-2 h-2 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              </svg>
              Owner
            </span>
          <% end %>
          <span class="text-xs text-gray-500">
            <%= time_ago_in_words(reply.created_at) %> ago
          </span>
          <% if reply.edited? %>
            <span class="text-xs text-gray-400 italic">(edited)</span>
          <% end %>
        </div>
        
        <!-- Reply Actions -->
        <div class="flex items-center space-x-1">
          <% if user_signed_in? %>
            <!-- Like Button -->
            <button class="group flex items-center space-x-1 px-2 py-1 rounded-lg transition-all duration-200 <%= reply.liked_by?(current_user) ? 'bg-red-100 text-red-600' : 'bg-white text-gray-600 hover:bg-red-50 hover:text-red-600' %>"
                    onclick="toggleLike('<%= reply.id %>', '<%= toggle_like_property_comment_path(reply) %>')">
              <svg class="w-3 h-3 group-hover:scale-110 transition-transform duration-200" fill="<%= reply.liked_by?(current_user) ? 'currentColor' : 'none' %>" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
              <span class="text-xs font-medium" id="like-count-<%= reply.id %>"><%= reply.likes_count %></span>
            </button>
            
            <!-- Edit/Delete Actions for Reply Owner -->
            <% if reply.can_be_edited_by?(current_user) || reply.can_be_deleted_by?(current_user) %>
              <div class="relative">
                <button class="p-1 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
                        onclick="toggleReplyDropdown('<%= reply.id %>')">
                  <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>
                <div id="reply-dropdown-<%= reply.id %>" class="hidden absolute right-0 mt-2 w-40 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                  <% if reply.can_be_edited_by?(current_user) %>
                    <button class="w-full text-left px-3 py-2 text-xs text-gray-700 hover:bg-gray-50 rounded-t-lg"
                            onclick="editReply('<%= reply.id %>')">
                      Edit Reply
                    </button>
                  <% end %>
                  <% if reply.can_be_deleted_by?(current_user) %>
                    <%= link_to property_comment_path(reply), 
                                method: :delete,
                                confirm: "Are you sure you want to delete this reply?",
                                class: "block w-full text-left px-3 py-2 text-xs text-red-600 hover:bg-red-50 rounded-b-lg" do %>
                      Delete Reply
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% end %>
            
            <!-- Flag Button for Other Users -->
            <% if current_user != reply.user %>
              <%= link_to flag_property_comment_path(reply), 
                          method: :post,
                          confirm: "Are you sure you want to flag this reply as inappropriate?",
                          class: "p-1 text-gray-400 hover:text-red-500 rounded-lg transition-colors" do %>
                <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"></path>
                </svg>
              <% end %>
            <% end %>
          <% end %>
        </div>
      </div>
      
      <!-- Reply Text -->
      <div class="prose prose-gray max-w-none">
        <p class="text-gray-800 leading-relaxed text-sm"><%= simple_format(reply.display_content) %></p>
      </div>
    </div>
  </div>
</div>

<script>
function toggleReplyDropdown(replyId) {
  const dropdown = document.getElementById(`reply-dropdown-${replyId}`);
  dropdown.classList.toggle('hidden');
}

function editReply(replyId) {
  // This would open an edit modal or inline edit form
  alert('Edit reply functionality would be implemented here');
}

// Close reply dropdowns when clicking outside
document.addEventListener('click', function(event) {
  const dropdowns = document.querySelectorAll('[id^="reply-dropdown-"]');
  dropdowns.forEach(dropdown => {
    if (!dropdown.contains(event.target) && !event.target.closest('button')) {
      dropdown.classList.add('hidden');
    }
  });
});
</script>
