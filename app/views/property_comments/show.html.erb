<!-- Property Comment Show Page -->
<div class="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
  <!-- Header -->
  <div class="bg-white/80 backdrop-blur-xl border-b border-gray-200/50 sticky top-0 z-10">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <%= link_to property_path(@property), 
                      class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            Back to Property
          <% end %>
          
          <div>
            <h1 class="text-2xl font-bold text-gray-900">Comment Details</h1>
            <p class="text-gray-600">
              On <%= link_to @property.title, property_path(@property), class: "text-blue-600 hover:text-blue-800 font-medium" %>
            </p>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <% if user_signed_in? && @comment.can_be_edited_by?(current_user) %>
            <%= link_to edit_property_comment_path(@comment), 
                        class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
              </svg>
              Edit Comment
            <% end %>
          <% end %>
          
          <% if user_signed_in? && @comment.can_be_deleted_by?(current_user) %>
            <%= link_to property_comment_path(@comment), 
                        method: :delete,
                        confirm: "Are you sure you want to delete this comment?",
                        class: "inline-flex items-center px-4 py-2 bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
              </svg>
              Delete Comment
            <% end %>
          <% end %>
        </div>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Comment Card -->
    <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 mb-8">
      <!-- Comment Header -->
      <div class="flex items-start space-x-6 mb-6">
        <!-- User Avatar -->
        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center text-white font-bold text-2xl flex-shrink-0">
          <%= (@comment.user.name || @comment.user.email).first.upcase %>
        </div>
        
        <!-- Comment Info -->
        <div class="flex-1">
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center space-x-4">
              <h2 class="text-xl font-bold text-gray-900">
                <%= @comment.user.name || @comment.user.email.split('@').first.capitalize %>
              </h2>
              <% if @comment.user == @property.user %>
                <span class="inline-flex items-center px-3 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 text-sm font-bold rounded-2xl border border-blue-200">
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                  </svg>
                  Property Owner
                </span>
              <% end %>
            </div>
            
            <!-- Comment Actions -->
            <div class="flex items-center space-x-3">
              <% if user_signed_in? %>
                <!-- Like Button -->
                <button class="group flex items-center space-x-2 px-4 py-2 rounded-2xl transition-all duration-200 <%= @comment.liked_by?(current_user) ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600 hover:bg-red-50 hover:text-red-600' %>"
                        onclick="toggleLike('<%= @comment.id %>', '<%= toggle_like_property_comment_path(@comment) %>')">
                  <svg class="w-5 h-5 group-hover:scale-110 transition-transform duration-200" fill="<%= @comment.liked_by?(current_user) ? 'currentColor' : 'none' %>" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  </svg>
                  <span class="font-medium" id="like-count-<%= @comment.id %>"><%= @comment.likes_count %></span>
                  <span class="text-sm">likes</span>
                </button>
                
                <!-- Flag Button for Other Users -->
                <% if current_user != @comment.user %>
                  <%= link_to flag_property_comment_path(@comment), 
                              method: :post,
                              confirm: "Are you sure you want to flag this comment as inappropriate?",
                              class: "inline-flex items-center px-4 py-2 bg-gray-100 text-gray-600 hover:bg-red-50 hover:text-red-600 rounded-2xl transition-all duration-200" do %>
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"></path>
                    </svg>
                    Flag
                  <% end %>
                <% end %>
              <% end %>
            </div>
          </div>
          
          <!-- Timestamp and Edit Info -->
          <div class="flex items-center space-x-4 text-sm text-gray-500 mb-4">
            <span>Posted <%= time_ago_in_words(@comment.created_at) %> ago</span>
            <% if @comment.edited? %>
              <span class="italic">• Edited <%= time_ago_in_words(@comment.edited_at) %> ago</span>
            <% end %>
            <% if @comment.reply? %>
              <span>• Reply to 
                <%= link_to "parent comment", property_comment_path(@comment.parent), class: "text-blue-600 hover:text-blue-800" %>
              </span>
            <% end %>
          </div>
        </div>
      </div>
      
      <!-- Comment Content -->
      <div class="prose prose-lg prose-gray max-w-none mb-6">
        <div class="bg-gradient-to-r from-gray-50 to-white rounded-2xl p-6 border border-gray-200">
          <p class="text-gray-800 leading-relaxed text-lg"><%= simple_format(@comment.display_content) %></p>
        </div>
      </div>
    </div>

    <!-- Replies Section -->
    <% if @comment.replies.not_flagged.any? %>
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900">
            <%= pluralize(@comment.replies.not_flagged.count, 'Reply') %>
          </h3>
        </div>
        
        <div class="space-y-4">
          <% @comment.replies.not_flagged.oldest_first.each do |reply| %>
            <div class="pl-6 border-l-2 border-emerald-200">
              <%= render 'property_comments/reply', reply: reply %>
            </div>
          <% end %>
        </div>
      </div>
    <% end %>

    <!-- Reply Form -->
    <% if user_signed_in? && @comment.top_level? %>
      <div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8 mt-8">
        <div class="flex items-center space-x-3 mb-6">
          <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
            </svg>
          </div>
          <h3 class="text-xl font-bold text-gray-900">Reply to this comment</h3>
        </div>
        
        <%= form_with model: [@property, PropertyComment.new], 
                      url: property_property_comments_path(@property),
                      local: true,
                      class: "space-y-4" do |form| %>
          <%= form.hidden_field :parent_id, value: @comment.id %>
          
          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200">
            <div class="flex items-start space-x-4">
              <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                <%= (current_user.name || current_user.email).first.upcase %>
              </div>
              <div class="flex-1">
                <%= form.text_area :content, 
                                   placeholder: "Write your reply...",
                                   rows: 4,
                                   class: "w-full px-4 py-3 border border-blue-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-gray-900 placeholder-gray-500",
                                   maxlength: 2000,
                                   oninput: "updateCharacterCount(this)" %>
                <div class="flex items-center justify-between mt-3">
                  <div class="text-sm text-gray-500">
                    <span id="characterCount">0</span>/2000 characters
                  </div>
                  <div class="flex space-x-3">
                    <%= link_to "Cancel", property_path(@property), 
                                class: "px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors" %>
                    <%= form.submit "Post Reply", 
                                    class: "px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>
    <% end %>
  </div>
</div>

<!-- Include the same JavaScript functions -->
<script>
function toggleLike(commentId, url) {
  console.log('Toggling like for comment:', commentId, 'URL:', url);
  
  // Get CSRF token
  const csrfMeta = document.querySelector('meta[name="csrf-token"]');
  if (!csrfMeta) {
    console.error('CSRF token not found');
    alert('Security token not found. Please refresh the page.');
    return;
  }
  
  const token = csrfMeta.getAttribute('content');
  
  fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'X-CSRF-Token': token,
      'Accept': 'application/json'
    }
  })
  .then(response => {
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    return response.json();
  })
  .then(data => {
    if (data.liked !== undefined) {
      // Update like count
      const countElement = document.getElementById(`like-count-${commentId}`);
      if (countElement) {
        countElement.textContent = data.likes_count;
      }
      
      // Update button appearance
      const button = countElement.closest('button');
      if (button) {
        const svg = button.querySelector('svg');
        if (data.liked) {
          button.className = button.className.replace('bg-gray-100 text-gray-600', 'bg-red-100 text-red-600');
          if (svg) {
            svg.setAttribute('fill', 'currentColor');
          }
        } else {
          button.className = button.className.replace('bg-red-100 text-red-600', 'bg-gray-100 text-gray-600');
          if (svg) {
            svg.setAttribute('fill', 'none');
          }
        }
      }
    }
  })
  .catch(error => {
    console.error('Error toggling like:', error);
    alert('Failed to update like. Please try again.');
  });
}

function updateCharacterCount(textarea) {
  const count = textarea.value.length;
  document.getElementById('characterCount').textContent = count;
  
  // Change color based on character count
  const counter = document.getElementById('characterCount');
  if (count > 1800) {
    counter.className = 'text-red-500 font-bold';
  } else if (count > 1500) {
    counter.className = 'text-amber-500 font-bold';
  } else {
    counter.className = 'text-gray-500';
  }
}
</script>
