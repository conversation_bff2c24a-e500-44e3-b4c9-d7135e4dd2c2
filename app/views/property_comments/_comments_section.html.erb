<!-- Enhanced Comments Section -->
<div class="bg-white/80 backdrop-blur-xl rounded-3xl shadow-xl shadow-gray-200/50 border border-white/20 p-8" data-controller="comments">
  <div class="flex items-center justify-between mb-8">
    <div class="flex items-center space-x-3">
      <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl flex items-center justify-center">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
        </svg>
      </div>
      <div>
        <h3 class="text-3xl font-bold text-gray-900">Comments</h3>
        <p class="text-gray-600 font-medium">
          <%= pluralize(@property.comments_count, 'comment') %>
        </p>
      </div>
    </div>
    
    <% if user_signed_in? %>
      <button class="group inline-flex items-center px-6 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
              data-action="click->comments#toggleCommentForm">
        <svg class="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        Add Comment
      </button>
    <% end %>
  </div>

  <!-- Comment Form -->
  <% if user_signed_in? %>
    <div class="mb-8 hidden" data-comments-target="commentForm">
      <%= form_with model: [@property, PropertyComment.new], 
                    url: property_property_comments_path(@property),
                    local: true, 
                    class: "space-y-4",
                    data: { 
                      controller: "form-validation",
                      action: "submit->comments#submitComment"
                    } do |form| %>
        
        <div class="bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl p-6 border border-gray-200">
          <div class="flex items-start space-x-4">
            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
              <%= (current_user.name || current_user.email).first.upcase %>
            </div>
            <div class="flex-1">
              <%= form.text_area :content, 
                                 placeholder: "Share your thoughts about this property...",
                                 rows: 4,
                                 class: "w-full px-4 py-3 border border-gray-300 rounded-2xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-gray-900 placeholder-gray-500",
                                 maxlength: 2000,
                                 data: { 
                                   "form-validation-target": "field",
                                   "validation-required": "true",
                                   "validation-min-length": "1",
                                   "validation-max-length": "2000"
                                 } %>
              <div class="flex items-center justify-between mt-3">
                <div class="text-sm text-gray-500">
                  <span data-comments-target="characterCount">0</span>/2000 characters
                </div>
                <div class="flex space-x-3">
                  <button type="button" 
                          class="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
                          data-action="click->comments#toggleCommentForm">
                    Cancel
                  </button>
                  <%= form.submit "Post Comment", 
                                  class: "px-6 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed",
                                  data: { "form-validation-target": "submit" } %>
                </div>
              </div>
            </div>
          </div>
        </div>
      <% end %>
    </div>
  <% else %>
    <div class="mb-8 text-center p-6 bg-gradient-to-r from-gray-50 to-blue-50 rounded-2xl border border-gray-200">
      <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-3xl flex items-center justify-center mx-auto mb-4">
        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      <p class="text-gray-700 font-semibold mb-4">Sign in to join the conversation</p>
      <%= link_to login_path, class: "inline-block bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3 rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
        Sign In to Comment
      <% end %>
    </div>
  <% end %>

  <!-- Comments List -->
  <div class="space-y-6" data-comments-target="commentsList">
    <% if @property.property_comments.not_flagged.any? %>
      <% @property.recent_comments(10).each do |comment| %>
        <%= render 'property_comments/comment', comment: comment %>
      <% end %>
      
      <% if @property.comments_count > 10 %>
        <div class="text-center pt-6">
          <%= link_to property_property_comments_path(@property), 
                      class: "inline-flex items-center px-6 py-3 bg-gradient-to-r from-gray-600 to-gray-700 hover:from-gray-700 hover:to-gray-800 text-white rounded-2xl font-bold transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5" do %>
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
            </svg>
            View All <%= @property.comments_count %> Comments
          <% end %>
        </div>
      <% end %>
    <% else %>
      <div class="text-center py-12">
        <div class="w-20 h-20 bg-gradient-to-r from-gray-200 to-gray-300 rounded-3xl flex items-center justify-center mx-auto mb-6">
          <svg class="w-10 h-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
        </div>
        <h4 class="text-xl font-bold text-gray-600 mb-2">No comments yet</h4>
        <p class="text-gray-500">Be the first to share your thoughts about this property!</p>
      </div>
    <% end %>
  </div>
</div>
