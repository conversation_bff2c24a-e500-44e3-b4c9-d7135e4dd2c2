<div class="bg-gradient-to-r from-gray-50 to-white rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300">
  <div class="flex items-start space-x-4">
    <!-- User Avatar -->
    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
      <%= (comment.user.name || comment.user.email).first.upcase %>
    </div>
    
    <!-- Comment Content -->
    <div class="flex-1 min-w-0">
      <!-- User Info and Timestamp -->
      <div class="flex items-center justify-between mb-3">
        <div class="flex items-center space-x-3">
          <h4 class="font-bold text-gray-900">
            <%= comment.user.name || comment.user.email.split('@').first.capitalize %>
          </h4>
          <% if comment.user == @property.user %>
            <span class="inline-flex items-center px-2 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 text-xs font-bold rounded-xl border border-blue-200">
              <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
              </svg>
              Property Owner
            </span>
          <% end %>
          <span class="text-sm text-gray-500">
            <%= time_ago_in_words(comment.created_at) %> ago
          </span>
          <% if comment.edited? %>
            <span class="text-xs text-gray-400 italic">(edited)</span>
          <% end %>
        </div>
        
        <!-- Comment Actions -->
        <div class="flex items-center space-x-2">
          <% if user_signed_in? %>
            <!-- Like Button -->
            <%= link_to toggle_like_property_comment_path(comment), 
                        method: :post,
                        class: "group flex items-center space-x-1 px-3 py-1 rounded-xl transition-all duration-200 #{comment.liked_by?(current_user) ? 'bg-red-100 text-red-600' : 'bg-gray-100 text-gray-600 hover:bg-red-50 hover:text-red-600'}" do %>
              <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="<%= comment.liked_by?(current_user) ? 'currentColor' : 'none' %>" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
              <span class="text-sm font-medium"><%= comment.likes_count %></span>
            <% end %>
            
            <!-- Reply Button -->
            <button class="flex items-center space-x-1 px-3 py-1 bg-gray-100 text-gray-600 hover:bg-blue-50 hover:text-blue-600 rounded-xl transition-all duration-200"
                    onclick="toggleReplyForm('<%= comment.id %>')">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6"></path>
              </svg>
              <span class="text-sm font-medium">Reply</span>
            </button>
            
            <!-- Edit/Delete Actions for Comment Owner -->
            <% if comment.can_be_edited_by?(current_user) || comment.can_be_deleted_by?(current_user) %>
              <div class="relative">
                <button class="p-1 text-gray-400 hover:text-gray-600 rounded-lg transition-colors"
                        onclick="toggleDropdown('<%= comment.id %>')">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"></path>
                  </svg>
                </button>
                <div id="dropdown-<%= comment.id %>" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-xl shadow-lg border border-gray-200 z-10">
                  <% if comment.can_be_edited_by?(current_user) %>
                    <button class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-t-xl"
                            onclick="editComment('<%= comment.id %>')">
                      Edit Comment
                    </button>
                  <% end %>
                  <% if comment.can_be_deleted_by?(current_user) %>
                    <%= link_to property_comment_path(comment), 
                                method: :delete,
                                confirm: "Are you sure you want to delete this comment?",
                                class: "block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 rounded-b-xl" do %>
                      Delete Comment
                    <% end %>
                  <% end %>
                </div>
              </div>
            <% end %>
            
            <!-- Flag Button for Other Users -->
            <% if current_user != comment.user %>
              <%= link_to flag_property_comment_path(comment), 
                          method: :post,
                          confirm: "Are you sure you want to flag this comment as inappropriate?",
                          class: "p-1 text-gray-400 hover:text-red-500 rounded-lg transition-colors" do %>
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 21v-4m0 0V5a2 2 0 012-2h6.5l1 1H21l-3 6 3 6h-8.5l-1-1H5a2 2 0 00-2 2zm9-13.5V9"></path>
                </svg>
              <% end %>
            <% end %>
          <% end %>
        </div>
      </div>
      
      <!-- Comment Text -->
      <div class="prose prose-gray max-w-none mb-4">
        <p class="text-gray-800 leading-relaxed"><%= simple_format(comment.display_content) %></p>
      </div>
      
      <!-- Reply Form -->
      <% if user_signed_in? %>
        <div id="reply-form-<%= comment.id %>" class="hidden mt-4">
          <%= form_with model: [@property, PropertyComment.new], 
                        url: property_property_comments_path(@property),
                        local: true,
                        class: "bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-200" do |form| %>
            <%= form.hidden_field :parent_id, value: comment.id %>
            <div class="flex items-start space-x-3">
              <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center text-white font-bold text-sm flex-shrink-0">
                <%= (current_user.name || current_user.email).first.upcase %>
              </div>
              <div class="flex-1">
                <%= form.text_area :content, 
                                   placeholder: "Write a reply...",
                                   rows: 2,
                                   class: "w-full px-3 py-2 border border-blue-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-gray-900 placeholder-gray-500 text-sm" %>
                <div class="flex justify-end space-x-2 mt-2">
                  <button type="button" 
                          class="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 transition-colors"
                          onclick="toggleReplyForm('<%= comment.id %>')">
                    Cancel
                  </button>
                  <%= form.submit "Reply", 
                                  class: "px-4 py-1 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white rounded-lg text-sm font-medium transition-all duration-300" %>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      <% end %>
      
      <!-- Replies -->
      <% if comment.replies.not_flagged.any? %>
        <div class="mt-6 space-y-4">
          <% comment.replies.not_flagged.oldest_first.each do |reply| %>
            <div class="ml-6 pl-4 border-l-2 border-gray-200">
              <%= render 'property_comments/reply', reply: reply %>
            </div>
          <% end %>
        </div>
      <% end %>
    </div>
  </div>
</div>

<script>
function toggleReplyForm(commentId) {
  const form = document.getElementById(`reply-form-${commentId}`);
  form.classList.toggle('hidden');
  if (!form.classList.contains('hidden')) {
    form.querySelector('textarea').focus();
  }
}

function toggleDropdown(commentId) {
  const dropdown = document.getElementById(`dropdown-${commentId}`);
  dropdown.classList.toggle('hidden');
}

function editComment(commentId) {
  // This would open an edit modal or inline edit form
  alert('Edit functionality would be implemented here');
}

// Close dropdowns when clicking outside
document.addEventListener('click', function(event) {
  const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
  dropdowns.forEach(dropdown => {
    if (!dropdown.contains(event.target) && !event.target.closest('button')) {
      dropdown.classList.add('hidden');
    }
  });
});
</script>
