<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    
    <title><%= content_for?(:title) ? yield(:title) : "Dashboard - Ofie" %></title>
    <meta name="description" content="<%= content_for?(:description) ? yield(:description) : "Manage your properties, applications, and rental business with <PERSON><PERSON>'s comprehensive dashboard." %>">
    <meta name="keywords" content="<%= content_for?(:keywords) ? yield(:keywords) : "property management, rental dashboard, landlord tools, tenant portal" %>">
    

    
    <!-- Stylesheets -->
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "inter-font", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    
    <!-- JavaScript -->
    <%= javascript_importmap_tags %>
    
    <!-- Additional head content -->
    <%= yield :head %>
  </head>

  <body class="h-full overflow-hidden" data-controller="sidebar notifications">
    <!-- Dashboard Layout Container -->
    <div class="flex h-full">
      <!-- Sidebar -->
      <%= render 'shared/dashboard_sidebar' %>
      
      <!-- Main Content Area -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Enhanced Top Navigation Bar -->
        <header class="bg-white/95 backdrop-blur-xl shadow-xl shadow-gray-200/20 border-b border-gray-200/50 z-30">
          <div class="flex items-center justify-between h-20 px-6">
            <!-- Mobile Menu Button with Enhanced Icon -->
            <button class="lg:hidden p-3 rounded-2xl text-gray-500 hover:text-gray-700 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
                    data-action="click->sidebar#open">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <g opacity="0.8">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2.5" d="M4 6h16M4 12h16M4 18h16"></path>
                  <circle cx="2" cy="6" r="1" fill="currentColor" opacity="0.6"/>
                  <circle cx="2" cy="12" r="1" fill="currentColor" opacity="0.6"/>
                  <circle cx="2" cy="18" r="1" fill="currentColor" opacity="0.6"/>
                </g>
              </svg>
            </button>

            <!-- Enhanced Page Title with Icon -->
            <div class="flex-1 lg:flex-none">
              <div class="flex items-center space-x-3">
                <div class="hidden lg:flex w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl items-center justify-center shadow-lg shadow-blue-500/25">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <g opacity="0.9">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5v6m8-6v6m-8-2h8"></path>
                      <circle cx="7" cy="9" r="1" fill="currentColor" opacity="0.7"/>
                      <circle cx="17" cy="9" r="1" fill="currentColor" opacity="0.7"/>
                    </g>
                  </svg>
                </div>
                <h1 class="text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  <%= content_for?(:page_title) ? yield(:page_title) : "Dashboard" %>
                </h1>
              </div>
            </div>
            
            <!-- Right Side Actions -->
            <div class="flex items-center space-x-4">
              <!-- Search (Optional) -->
              <% if content_for?(:search_bar) %>
                <%= yield :search_bar %>
              <% end %>
              
              <!-- Enhanced Notifications -->
              <div class="relative" data-controller="notifications">
                <button data-action="click->notifications#toggle"
                        class="relative p-3 text-gray-500 hover:text-gray-700 hover:bg-gradient-to-r hover:from-amber-50 hover:to-orange-50 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <g opacity="0.9">
                      <!-- Bell body -->
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
                      <!-- Bell details -->
                      <circle cx="12" cy="6" r="1.5" fill="currentColor" opacity="0.3"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 17v1a3 3 0 006 0v-1" opacity="0.7"/>
                      <!-- Sound waves -->
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M18 8a6 6 0 00-12 0" opacity="0.4"/>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M19 5a9 9 0 00-14 0" opacity="0.3"/>
                    </g>
                  </svg>
                  <!-- Notification Badge -->
                  <% if @unread_notifications_count && @unread_notifications_count > 0 %>
                    <span class="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full flex items-center justify-center">
                      <%= @unread_notifications_count > 9 ? '9+' : @unread_notifications_count %>
                    </span>
                  <% end %>
                </button>
                
                <!-- Enhanced Notification Dropdown -->
                <div data-notifications-target="dropdown" class="hidden absolute right-0 mt-3 w-96 bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 z-50 overflow-hidden">
                  <div class="px-6 py-4 bg-gradient-to-r from-amber-50 to-orange-50 border-b border-gray-200/50">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-gradient-to-r from-amber-500 to-orange-500 rounded-lg flex items-center justify-center">
                          <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
                          </svg>
                        </div>
                        <h3 class="text-lg font-bold text-gray-900">Notifications</h3>
                      </div>
                      <button class="text-sm text-amber-600 hover:text-amber-800 font-medium transition-colors duration-200" data-action="click->notifications#markAllAsRead">
                        Mark all as read
                      </button>
                    </div>
                  </div>
                  <div class="max-h-80 overflow-y-auto" data-notifications-target="list">
                    <!-- Notifications will be loaded here -->
                    <div class="p-6 text-center text-gray-500">
                      <div class="w-16 h-16 mx-auto mb-4 bg-gradient-to-r from-gray-100 to-gray-200 rounded-2xl flex items-center justify-center">
                        <svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <g opacity="0.8">
                            <!-- Bell with details -->
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
                            <circle cx="12" cy="6" r="1" fill="currentColor" opacity="0.4"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 17v1a3 3 0 006 0v-1" opacity="0.6"/>
                            <!-- Peaceful state indicators -->
                            <circle cx="8" cy="10" r="0.5" fill="currentColor" opacity="0.3"/>
                            <circle cx="16" cy="10" r="0.5" fill="currentColor" opacity="0.3"/>
                          </g>
                        </svg>
                      </div>
                      <p class="text-sm font-medium">No new notifications</p>
                      <p class="text-xs text-gray-400 mt-1">You're all caught up!</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Enhanced User Menu -->
              <div class="relative" data-controller="dropdown">
                <button data-action="click->dropdown#toggle"
                        class="flex items-center space-x-3 p-3 text-gray-500 hover:text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <% if current_user.avatar.attached? %>
                    <div class="relative">
                      <%= image_tag current_user.avatar, class: "h-10 w-10 rounded-2xl object-cover border-2 border-white shadow-lg", alt: "#{current_user.name} avatar" %>
                      <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white"></div>
                    </div>
                  <% else %>
                    <div class="relative h-10 w-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25">
                      <span class="text-white font-bold text-lg"><%= current_user.name&.first&.upcase || current_user.email.first.upcase %></span>
                      <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white"></div>
                    </div>
                  <% end %>
                  <div class="hidden lg:block">
                    <p class="text-sm font-semibold text-gray-900 text-left">
                      <%= current_user.name || current_user.email.split('@').first.capitalize %>
                    </p>
                    <p class="text-xs text-gray-500 text-left">
                      <%= current_user.landlord? ? "Property Owner" : "Tenant" %>
                    </p>
                  </div>
                  <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <g opacity="0.8">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                      <circle cx="12" cy="12" r="1" fill="currentColor" opacity="0.5"/>
                    </g>
                  </svg>
                </button>
                
                <!-- User Dropdown Menu -->
                <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-56 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl shadow-gray-200/50 border border-white/20 z-50 overflow-hidden">
                  <div class="px-4 py-3 border-b border-gray-200/50">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      <%= current_user.name || current_user.email.split('@').first.capitalize %>
                    </p>
                    <p class="text-xs text-gray-500 truncate">
                      <%= current_user.email %>
                    </p>
                  </div>
                  
                  <div class="py-2">
                    <%= link_to edit_profile_path, class: "flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 rounded-xl mx-2 transition-all duration-200 group" do %>
                      <div class="w-8 h-8 rounded-xl bg-gradient-to-r from-blue-100 to-indigo-100 flex items-center justify-center mr-3 group-hover:from-blue-200 group-hover:to-indigo-200 transition-all duration-200">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <g opacity="0.9">
                            <!-- User profile icon with details -->
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            <circle cx="12" cy="7" r="1" fill="currentColor" opacity="0.4"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19h6" opacity="0.6"/>
                          </g>
                        </svg>
                      </div>
                      <span class="font-medium">Profile Settings</span>
                    <% end %>

                    <%= link_to help_path, class: "flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gradient-to-r hover:from-amber-50 hover:to-orange-50 rounded-xl mx-2 transition-all duration-200 group" do %>
                      <div class="w-8 h-8 rounded-xl bg-gradient-to-r from-amber-100 to-orange-100 flex items-center justify-center mr-3 group-hover:from-amber-200 group-hover:to-orange-200 transition-all duration-200">
                        <svg class="w-4 h-4 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <g opacity="0.9">
                            <!-- Help icon with question mark and support elements -->
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            <circle cx="12" cy="19" r="1" fill="currentColor" opacity="0.6"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M15 9a3 3 0 11-6 0 3 3 0 016 0z" opacity="0.4"/>
                          </g>
                        </svg>
                      </div>
                      <span class="font-medium">Help & Support</span>
                    <% end %>
                  </div>

                  <div class="border-t border-gray-200/50 py-2">
                    <%= link_to logout_path, method: :delete,
                        class: "flex items-center px-4 py-3 text-sm text-red-600 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 rounded-xl mx-2 transition-all duration-200 group" do %>
                      <div class="w-8 h-8 rounded-xl bg-gradient-to-r from-red-100 to-pink-100 flex items-center justify-center mr-3 group-hover:from-red-200 group-hover:to-pink-200 transition-all duration-200">
                        <svg class="w-4 h-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <g opacity="0.9">
                            <!-- Logout icon with door and arrow -->
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            <rect x="3" y="6" width="2" height="12" rx="1" fill="currentColor" opacity="0.3"/>
                            <circle cx="19" cy="12" r="1" fill="currentColor" opacity="0.5"/>
                          </g>
                        </svg>
                      </div>
                      <span class="font-medium">Sign Out</span>
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>
        
        <!-- Main Content -->
        <main class="flex-1 overflow-y-auto bg-gray-50">
          <!-- Flash Messages -->
          <% if notice %>
            <div class="bg-green-50 border-l-4 border-green-400 p-4 m-4 rounded-r-lg">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-green-700"><%= notice %></p>
                </div>
              </div>
            </div>
          <% end %>
          
          <% if alert %>
            <div class="bg-red-50 border-l-4 border-red-400 p-4 m-4 rounded-r-lg">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-red-700"><%= alert %></p>
                </div>
              </div>
            </div>
          <% end %>
          
          <!-- Page Content -->
          <%= yield %>
        </main>
      </div>
    </div>
    
    <!-- Additional body content -->
    <%= yield :body %>
  </body>
</html>
