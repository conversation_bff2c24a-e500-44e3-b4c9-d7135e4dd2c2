<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
  <head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    
    <title><%= content_for?(:title) ? yield(:title) : "Dashboard - Ofie" %></title>
    <meta name="description" content="<%= content_for?(:description) ? yield(:description) : "Manage your properties, applications, and rental business with <PERSON><PERSON>'s comprehensive dashboard." %>">
    <meta name="keywords" content="<%= content_for?(:keywords) ? yield(:keywords) : "property management, rental dashboard, landlord tools, tenant portal" %>">
    
    <!-- Favicon -->
    <!-- <%= favicon_link_tag "favicon.ico" %> -->
    
    <!-- Stylesheets -->
    <%= csrf_meta_tags %>
    <%= csp_meta_tag %>
    <%= stylesheet_link_tag "tailwind", "inter-font", "data-turbo-track": "reload" %>
    <%= stylesheet_link_tag "application", "data-turbo-track": "reload" %>
    
    <!-- JavaScript -->
    <%= javascript_importmap_tags %>
    
    <!-- Additional head content -->
    <%= yield :head %>
  </head>

  <body class="h-full overflow-hidden" data-controller="sidebar notifications">
    <!-- Dashboard Layout Container -->
    <div class="flex h-full">
      <!-- Sidebar -->
      <%= render 'shared/dashboard_sidebar' %>
      
      <!-- Main Content Area -->
      <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top Navigation Bar -->
        <header class="bg-white/95 backdrop-blur-xl shadow-sm border-b border-gray-200/50 z-30">
          <div class="flex items-center justify-between h-16 px-6">
            <!-- Mobile Menu Button -->
            <button class="lg:hidden p-2 rounded-xl text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                    data-action="click->sidebar#open">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
              </svg>
            </button>
            
            <!-- Page Title -->
            <div class="flex-1 lg:flex-none">
              <h1 class="text-xl font-semibold text-gray-900">
                <%= content_for?(:page_title) ? yield(:page_title) : "Dashboard" %>
              </h1>
            </div>
            
            <!-- Right Side Actions -->
            <div class="flex items-center space-x-4">
              <!-- Search (Optional) -->
              <% if content_for?(:search_bar) %>
                <%= yield :search_bar %>
              <% end %>
              
              <!-- Notifications -->
              <div class="relative" data-controller="notifications">
                <button data-action="click->notifications#toggle" 
                        class="relative p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
                  </svg>
                  <!-- Notification Badge -->
                  <% if @unread_notifications_count && @unread_notifications_count > 0 %>
                    <span class="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full flex items-center justify-center">
                      <%= @unread_notifications_count > 9 ? '9+' : @unread_notifications_count %>
                    </span>
                  <% end %>
                </button>
                
                <!-- Notification Dropdown -->
                <div data-notifications-target="dropdown" class="hidden absolute right-0 mt-3 w-96 bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 z-50 overflow-hidden">
                  <div class="px-6 py-4 bg-gradient-to-r from-amber-50 to-orange-50 border-b border-gray-200/50">
                    <div class="flex items-center justify-between">
                      <h3 class="text-lg font-bold text-gray-900">Notifications</h3>
                      <button class="text-sm text-amber-600 hover:text-amber-800 font-medium" data-action="click->notifications#markAllAsRead">
                        Mark all as read
                      </button>
                    </div>
                  </div>
                  <div class="max-h-80 overflow-y-auto" data-notifications-target="list">
                    <!-- Notifications will be loaded here -->
                    <div class="p-6 text-center text-gray-500">
                      <svg class="w-8 h-8 mx-auto mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.5 3.5a6 6 0 0 1 6 6v2l1.5 3h-15l1.5-3v-2a6 6 0 0 1 6-6z"></path>
                      </svg>
                      <p>No new notifications</p>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- User Menu -->
              <div class="relative" data-controller="dropdown">
                <button data-action="click->dropdown#toggle" 
                        class="flex items-center space-x-2 p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-xl transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                  <% if current_user.avatar.attached? %>
                    <%= image_tag current_user.avatar, class: "h-8 w-8 rounded-xl object-cover", alt: "#{current_user.name} avatar" %>
                  <% else %>
                    <div class="h-8 w-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center">
                      <span class="text-white font-semibold text-sm"><%= current_user.name&.first&.upcase || current_user.email.first.upcase %></span>
                    </div>
                  <% end %>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                
                <!-- User Dropdown Menu -->
                <div data-dropdown-target="menu" class="hidden absolute right-0 mt-2 w-56 bg-white/95 backdrop-blur-xl rounded-2xl shadow-xl shadow-gray-200/50 border border-white/20 z-50 overflow-hidden">
                  <div class="px-4 py-3 border-b border-gray-200/50">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      <%= current_user.name || current_user.email.split('@').first.capitalize %>
                    </p>
                    <p class="text-xs text-gray-500 truncate">
                      <%= current_user.email %>
                    </p>
                  </div>
                  
                  <div class="py-2">
                    <%= link_to edit_user_registration_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200" do %>
                      <svg class="w-4 h-4 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                      </svg>
                      Profile Settings
                    <% end %>
                    
                    <%= link_to help_path, class: "flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200" do %>
                      <svg class="w-4 h-4 mr-3 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                      Help & Support
                    <% end %>
                  </div>
                  
                  <div class="border-t border-gray-200/50 py-2">
                    <%= link_to destroy_user_session_path, method: :delete, 
                        class: "flex items-center px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200" do %>
                      <svg class="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                      </svg>
                      Sign Out
                    <% end %>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </header>
        
        <!-- Main Content -->
        <main class="flex-1 overflow-y-auto bg-gray-50">
          <!-- Flash Messages -->
          <% if notice %>
            <div class="bg-green-50 border-l-4 border-green-400 p-4 m-4 rounded-r-lg">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-green-700"><%= notice %></p>
                </div>
              </div>
            </div>
          <% end %>
          
          <% if alert %>
            <div class="bg-red-50 border-l-4 border-red-400 p-4 m-4 rounded-r-lg">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                  </svg>
                </div>
                <div class="ml-3">
                  <p class="text-sm text-red-700"><%= alert %></p>
                </div>
              </div>
            </div>
          <% end %>
          
          <!-- Page Content -->
          <%= yield %>
        </main>
      </div>
    </div>
    
    <!-- Additional body content -->
    <%= yield :body %>
  </body>
</html>
