<!-- Enhanced Sophisticated Navigation -->
<nav class="bg-white/90 backdrop-blur-xl shadow-xl shadow-gray-200/20 border-b border-white/20 sticky top-0 z-50 transition-all duration-300" 
     role="navigation" 
     aria-label="Main navigation"
     data-controller="navbar"
     data-navbar-scrolled-class="shadow-2xl bg-white/95">
  
  <!-- Gradient overlay for added depth -->
  <div class="absolute inset-0 bg-gradient-to-r from-blue-50/50 via-transparent to-indigo-50/50 pointer-events-none"></div>
  
  <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between h-20">
      <!-- Enhanced Logo and Brand -->
      <div class="flex items-center">
        <%= link_to root_path, class: "group flex items-center space-x-3 hover:opacity-90 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-2xl p-2 transform hover:scale-105", 
            'aria-label': "Ofie - Go to homepage" do %>
          <div class="relative">
            <div class="h-12 w-12 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-800 rounded-2xl flex items-center justify-center shadow-lg shadow-blue-500/25 group-hover:shadow-xl group-hover:shadow-blue-500/30 transition-all duration-300">
              <svg class="h-7 w-7 text-white group-hover:scale-110 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
              </svg>
            </div>
            <div class="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-r from-emerald-400 to-green-500 rounded-full border-2 border-white animate-pulse"></div>
          </div>
          <div>
            <span class="text-3xl font-bold bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-800 bg-clip-text text-transparent group-hover:from-blue-700 group-hover:to-indigo-900 transition-all duration-300">Ofie</span>
            <div class="text-xs font-medium text-gray-500 -mt-1">Property Platform</div>
          </div>
        <% end %>
      </div>
      
      <!-- Enhanced Search Bar (Desktop) -->
      <div class="hidden lg:flex flex-1 max-w-lg mx-6">
        <div class="relative w-full group" data-controller="search" data-search-url-value="/properties/search">
          <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400 group-focus-within:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
          <input data-search-target="input" 
                 data-action="input->search#search focus->search#showResults blur->search#hideResults" 
                 type="text" 
                 placeholder="Search properties, locations, or neighborhoods..." 
                 class="block w-full pl-12 pr-4 py-3 bg-white/80 backdrop-blur-sm border-2 border-gray-200 rounded-2xl leading-5 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white transition-all duration-300 text-sm font-medium shadow-sm hover:shadow-md hover:border-gray-300 group-focus-within:shadow-lg">
          
          <!-- Enhanced Search Results Dropdown -->
          <div data-search-target="results" class="hidden absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl shadow-gray-200/50 border border-white/20 max-h-96 overflow-y-auto z-50">
            <!-- Results will be populated here -->
          </div>
        </div>
      </div>
      
      <!-- Enhanced Navigation Links -->
      <div class="hidden lg:flex items-center space-x-2">
        <%= link_to "Browse", properties_path, class: "group text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:-translate-y-0.5" %>
        <%= link_to "Demo", demo_flash_demo_path, class: "group text-gray-700 hover:text-purple-600 hover:bg-purple-50 px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transform hover:-translate-y-0.5" %>
        
        <% if user_signed_in? %>
          <% if current_user.landlord? %>
            <%= link_to "Dashboard", dashboard_path, class: "group text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:-translate-y-0.5" %>
            <%= link_to new_property_path, class: "group bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-2 rounded-xl text-sm font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 ml-3 shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/30 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:-translate-y-0.5" do %>
              <svg class="w-4 h-4 inline mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Add Property
            <% end %>
          <% else %>
            <%= link_to "Dashboard", dashboard_path, class: "group text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:-translate-y-0.5" %>
            <!-- Enhanced Favorites with notification badge -->
            <%= link_to "#", class: "group relative text-gray-700 hover:text-pink-600 hover:bg-pink-50 px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 transform hover:-translate-y-0.5" do %>
              <svg class="w-4 h-4 inline mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
              </svg>
              Favorites
              <span class="absolute -top-1 -right-1 h-5 w-5 bg-gradient-to-r from-pink-500 to-red-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse shadow-lg">3</span>
            <% end %>
          <% end %>
          
          <!-- Enhanced Notifications -->
          <div class="relative ml-2" data-controller="notifications">
            <button class="group relative p-3 text-gray-700 hover:text-amber-600 hover:bg-amber-50 rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 transform hover:-translate-y-0.5"
                aria-label="Notifications"
                data-action="click->notifications#toggle">
              <svg class="h-5 w-5 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0M3.124 7.5A8.969 8.969 0 015.292 3m13.416 0a8.969 8.969 0 012.168 4.5" />
              </svg>
              <span id="notification-badge" class="absolute top-1 right-1 h-3 w-3 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse shadow-lg" data-notifications-target="badge"></span>
              <span class="absolute top-1 right-1 h-3 w-3 bg-red-400 rounded-full animate-ping"></span>
            </button>

            <!-- Notification Dropdown -->
            <div data-notifications-target="dropdown" class="hidden absolute right-0 mt-3 w-96 bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 z-50 overflow-hidden">
              <div class="px-6 py-4 bg-gradient-to-r from-amber-50 to-orange-50 border-b border-gray-200/50">
                <div class="flex items-center justify-between">
                  <h3 class="text-lg font-bold text-gray-900">Notifications</h3>
                  <button class="text-sm text-amber-600 hover:text-amber-800 font-medium" data-action="click->notifications#markAllAsRead">
                    Mark all as read
                  </button>
                </div>
              </div>
              <div class="max-h-80 overflow-y-auto" data-notifications-target="list">
                <!-- Notifications will be loaded here -->
                <div class="p-6 text-center text-gray-500">
                  <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0M3.124 7.5A8.969 8.969 0 015.292 3m13.416 0a8.969 8.969 0 012.168 4.5" />
                  </svg>
                  <p>No new notifications</p>
                </div>
              </div>
              <div class="px-6 py-4 bg-gradient-to-r from-gray-50 to-white border-t border-gray-200/50">
                <%= link_to notifications_path, class: "block w-full text-center py-2 text-amber-600 hover:text-amber-800 font-medium transition-colors" do %>
                  View All Notifications
                <% end %>
              </div>
            </div>
          </div>
          
          <!-- Enhanced User Dropdown -->
          <div class="relative ml-4" data-controller="dropdown">
            <button data-action="click->dropdown#toggle" class="group flex items-center space-x-3 text-gray-700 hover:text-blue-600 px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-200 border-2 border-transparent hover:border-blue-200 hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:-translate-y-0.5"
                    aria-label="User menu"
                    aria-expanded="false"
                    aria-haspopup="true">
              <% if current_user.avatar.attached? %>
                <div class="relative">
                  <%= image_tag current_user.avatar, class: "h-10 w-10 rounded-xl object-cover border-2 border-white shadow-lg group-hover:shadow-xl transition-all duration-300", alt: "#{current_user.name} avatar" %>
                  <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white"></div>
                </div>
              <% else %>
                <div class="relative">
                  <div class="h-10 w-10 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center text-white font-bold text-sm shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                    <%= current_user.name.first.upcase %>
                  </div>
                  <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white"></div>
                </div>
              <% end %>
              <div class="hidden xl:block">
                <div class="text-left">
                  <p class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors duration-200"><%= current_user.name %></p>
                  <p class="text-xs text-gray-500"><%= current_user.role.humanize %></p>
                </div>
              </div>
              <svg class="h-4 w-4 transition-transform duration-200 group-hover:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </button>
            
            <!-- Enhanced Dropdown Menu -->
            <div data-dropdown-target="menu" class="hidden absolute right-0 mt-3 w-80 bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl shadow-gray-200/50 border border-white/20 z-50 overflow-hidden">
              <!-- Enhanced User Info Header -->
              <div class="px-6 py-5 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-gray-200/50">
                <div class="flex items-center space-x-4">
                  <% if current_user.avatar.attached? %>
                    <div class="relative">
                      <%= image_tag current_user.avatar, class: "h-14 w-14 rounded-2xl object-cover border-3 border-white shadow-lg", alt: "#{current_user.name} avatar" %>
                      <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white"></div>
                    </div>
                  <% else %>
                    <div class="relative">
                      <div class="h-14 w-14 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-2xl flex items-center justify-center text-white font-bold text-lg shadow-lg">
                        <%= current_user.name.first.upcase %>
                      </div>
                      <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white"></div>
                    </div>
                  <% end %>
                  <div class="flex-1 min-w-0">
                    <p class="text-lg font-bold text-gray-900 truncate"><%= current_user.name %></p>
                    <p class="text-sm text-gray-600 truncate"><%= current_user.email %></p>
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 mt-2 shadow-sm">
                      <div class="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                      <%= current_user.role.humanize %>
                    </span>
                  </div>
                </div>
              </div>
              
              <!-- Enhanced Menu Items -->
              <div class="py-2">
                <%= link_to profile_path, class: "group flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200" do %>
                  <div class="w-10 h-10 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                    <svg class="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                  </div>
                  <div>
                    <p class="font-semibold">Profile</p>
                    <p class="text-xs text-gray-500">View and edit profile</p>
                  </div>
                <% end %>
                
                <%= link_to settings_path, class: "group flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-all duration-200" do %>
                  <div class="w-10 h-10 bg-gradient-to-r from-purple-100 to-indigo-100 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                    <svg class="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <p class="font-semibold">Settings</p>
                    <p class="text-xs text-gray-500">Account preferences</p>
                  </div>
                <% end %>
                
                <% if current_user.landlord? %>
                  <%= link_to analytics_path, class: "group flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-600 transition-all duration-200" do %>
                    <div class="w-10 h-10 bg-gradient-to-r from-green-100 to-emerald-100 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                      <svg class="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                      </svg>
                    </div>
                    <div>
                      <p class="font-semibold">Analytics</p>
                      <p class="text-xs text-gray-500">Property insights</p>
                    </div>
                  <% end %>
                <% end %>
                
                <%= link_to conversations_path, class: "group flex items-center px-6 py-3 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 transition-all duration-200" do %>
                  <div class="w-10 h-10 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                    <svg class="h-5 w-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                  </div>
                  <div class="flex-1">
                    <p class="font-semibold">Messages</p>
                    <p class="text-xs text-gray-500">Chat with users</p>
                  </div>
                  <% if current_user.unread_messages_count > 0 %>
                    <span class="ml-auto h-5 w-5 bg-gradient-to-r from-red-500 to-pink-500 text-white text-xs rounded-full flex items-center justify-center animate-pulse">
                      <%= current_user.unread_messages_count > 9 ? '9+' : current_user.unread_messages_count %>
                    </span>
                  <% end %>
                <% end %>
                
                <hr class="my-2 border-gray-200">
                
                <%= link_to logout_path, method: :delete, class: "group flex items-center px-6 py-3 text-sm text-red-600 hover:bg-red-50 transition-all duration-200" do %>
                  <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center mr-3 group-hover:scale-110 transition-transform duration-200">
                    <svg class="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                    </svg>
                  </div>
                  <div>
                    <p class="font-semibold">Logout</p>
                    <p class="text-xs text-gray-500">Sign out of account</p>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% else %>
          <%= link_to "Login", login_path, class: "group text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-2 rounded-xl text-sm font-semibold transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:-translate-y-0.5" %>
          <%= link_to  register_path, class: "group bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-2 rounded-xl text-sm font-semibold hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 ml-3 shadow-lg shadow-blue-500/25 hover:shadow-xl hover:shadow-blue-500/30 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:-translate-y-0.5" do %>
            <svg class="w-4 h-4 inline mr-2 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
            </svg>
            Get Started
          <% end %>
        <% end %>
      </div>
      
      <!-- Enhanced Mobile menu button -->
      <div class="lg:hidden flex items-center space-x-3">
        <!-- Mobile Search Button -->
        <button data-controller="mobile-search" 
                data-action="click->mobile-search#toggle" 
                class="text-gray-700 hover:text-blue-600 hover:bg-blue-50 p-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 md:hidden transform hover:scale-105"
                aria-label="Toggle search">
          <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </button>
        
        <button data-controller="mobile-menu" 
                data-action="click->mobile-menu#toggle" 
                class="text-gray-700 hover:text-blue-600 hover:bg-blue-50 p-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105"
                aria-label="Toggle mobile menu"
                aria-expanded="false">
          <svg data-mobile-menu-target="openIcon" class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
          <svg data-mobile-menu-target="closeIcon" class="h-6 w-6 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Enhanced Mobile Search (appears on small screens) -->
  <div data-mobile-search-target="searchBar" class="hidden border-t border-gray-200/50 bg-white/90 backdrop-blur-xl px-4 py-4 md:hidden">
    <div class="relative">
      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
      </div>
      <input type="text" placeholder="Search properties..." 
             class="block w-full pl-10 pr-3 py-3 bg-white border-2 border-gray-200 rounded-2xl leading-5 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm">
    </div>
  </div>
  
  <!-- Enhanced Mobile menu -->
  <div data-mobile-menu-target="menu" class="hidden lg:hidden border-t border-gray-200/50 bg-white/95 backdrop-blur-xl transform translate-x-full transition-transform duration-300 ease-in-out">
    <!-- Mobile Search (for medium screens) -->
    <div class="px-4 py-4 border-b border-gray-200/50 hidden md:block lg:hidden">
      <div class="relative">
        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
          <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
          </svg>
        </div>
        <input type="text" placeholder="Search properties..." 
                class="block w-full pl-10 pr-3 py-3 bg-white border-2 border-gray-200 rounded-2xl leading-5 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 shadow-sm">
      </div>
    </div>
    
    <div class="px-3 pt-3 pb-4 space-y-2">
      <%= link_to properties_path, class: "group flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200", data: { action: "click->mobile-menu#linkClicked" } do %>
        <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        Browse Properties
      <% end %>
      
      <% if user_signed_in? %>
        <!-- Enhanced User Info Section -->
        <div class="px-4 py-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl mb-3 border border-blue-100">
          <div class="flex items-center space-x-3">
            <div class="relative">
              <% if current_user.avatar.attached? %>
                <%= image_tag current_user.avatar, class: "h-12 w-12 rounded-xl object-cover border-2 border-white shadow-lg", alt: "#{current_user.name} avatar" %>
              <% else %>
                <div class="h-12 w-12 bg-gradient-to-br from-blue-500 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center text-white font-bold">
                  <%= current_user.name.first.upcase %>
                </div>
              <% end %>
              <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border-2 border-white"></div>
            </div>
            <div class="flex-1">
              <p class="text-sm font-bold text-gray-900"><%= current_user.name %></p>
              <p class="text-xs text-gray-600"><%= current_user.email %></p>
              <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 mt-1">
                <div class="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-pulse"></div>
                <%= current_user.role.humanize %>
              </span>
            </div>
          </div>
        </div>
        
        <% if current_user.landlord? %>
          <%= link_to dashboard_path, class: "group flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200", data: { action: "click->mobile-menu#linkClicked" } do %>
            <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Dashboard
          <% end %>
          <%= link_to new_property_path, class: "group flex items-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-300 shadow-lg shadow-blue-500/25" do %>
            <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Add Property
          <% end %>
        <% else %>
          <%= link_to dashboard_path, class: "group flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200" do %>
            <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
            </svg>
            Dashboard
          <% end %>
          <%= link_to favorites_path, class: "group flex items-center text-gray-700 hover:text-pink-600 hover:bg-pink-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200 relative", data: { action: "click->mobile-menu#linkClicked" } do %>
            <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
            </svg>
            Favorites
            <% if current_user.property_favorites.count > 0 %>
              <span class="ml-auto h-6 w-6 bg-gradient-to-r from-pink-500 to-red-500 text-white text-xs rounded-full flex items-center justify-center font-semibold">
                <%= current_user.property_favorites.count > 9 ? '9+' : current_user.property_favorites.count %>
              </span>
            <% end %>
          <% end %>
        <% end %>
        
        <!-- Enhanced Navigation Items -->
        <%= link_to conversations_path, class: "group flex items-center text-gray-700 hover:text-purple-600 hover:bg-purple-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200 relative" do %>
          <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
          </svg>
          Messages
          <% if current_user.unread_messages_count > 0 %>
            <span class="ml-auto h-2 w-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse"></span>
          <% end %>
        <% end %>
        
        <%= link_to notifications_path, class: "group flex items-center text-gray-700 hover:text-amber-600 hover:bg-amber-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200 relative", data: { action: "click->mobile-menu#linkClicked" } do %>
          <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M14.857 17.082a23.848 23.848 0 005.454-1.31A8.967 8.967 0 0118 9.75v-.7V9A6 6 0 006 9v.75a8.967 8.967 0 01-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 01-5.714 0m5.714 0a3 3 0 11-5.714 0M3.124 7.5A8.969 8.969 0 015.292 3m13.416 0a8.969 8.969 0 012.168 4.5" />
          </svg>
          Notifications
          <% if current_user.notifications.unread.count > 0 %>
            <span class="ml-auto h-2 w-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse"></span>
          <% end %>
        <% end %>
        
        <%= link_to profile_path, class: "group flex items-center text-gray-700 hover:text-green-600 hover:bg-green-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200" do %>
          <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
          Profile
        <% end %>
        
        <%= link_to logout_path, method: :delete, class: "group flex items-center text-red-600 hover:bg-red-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200" do %>
          <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Logout
        <% end %>
      <% else %>
        <%= link_to login_path, class: "group flex items-center text-gray-700 hover:text-blue-600 hover:bg-blue-50 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-200" do %>
          <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
          </svg>
          Login
        <% end %>
        <%= link_to register_path, class: "group flex items-center bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 px-4 py-3 rounded-2xl text-base font-semibold transition-all duration-300 shadow-lg shadow-blue-500/25" do %>
          <svg class="h-5 w-5 mr-3 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
          </svg>
          Get Started
        <% end %>
      <% end %>
    </div>
  </div>
</nav>
